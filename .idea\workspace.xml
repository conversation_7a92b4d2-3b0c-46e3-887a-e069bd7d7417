<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="42b78599-eb22-4190-8327-ea29392e61fe" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://D:/Program Files/Go1.21" />
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="20Mfo7JGD1Dog8bmWixmoBKvBy2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultGoTemplateProperty&quot;: &quot;Go File&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.automatic.dependencies.download&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;E:/Work/TheService/DemonSlayer/master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Work\TheService\DemonSlayer\master\bin\csv" />
      <recent name="E:\Work\TheService\DemonSlayer\master\models" />
      <recent name="E:\Work\TheService\DemonSlayer\master\protocol" />
      <recent name="E:\Work\TheService\DemonSlayer\master\center\union" />
      <recent name="F:\Work\Service\server-dev\magic-dragon\dev-0.0.2\master\center\match" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Work\TheService\DemonSlayer\master\center\crossserver" />
      <recent name="E:\Work\TheService\DemonSlayer\master\models" />
      <recent name="E:\Work\TheService\DemonSlayer\master\model" />
      <recent name="E:\Work\TheService\DemonSlayer\master\core" />
    </key>
  </component>
  <component name="RunManager" selected="Go Build.master">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="master" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="master" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="master" />
      <working_directory value="$PROJECT_DIR$/bin" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <package value="master" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <output_directory value="$PROJECT_DIR$/bin" />
      <method v="2" />
    </configuration>
    <configuration name="TestJsonMarshal in master/utils" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="master" />
      <working_directory value="$PROJECT_DIR$/utils" />
      <kind value="PACKAGE" />
      <package value="master/utils" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <pattern value="^TestJsonMarshal$" />
      <method v="2" />
    </configuration>
    <configuration name="TestMapCreation in master/utils" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="master" />
      <working_directory value="$PROJECT_DIR$/utils" />
      <kind value="PACKAGE" />
      <package value="master/utils" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <pattern value="^TestMapCreation$" />
      <method v="2" />
    </configuration>
    <configuration name="TestRandArr in master/utils" type="GoTestRunConfiguration" factoryName="Go Test" temporary="true" nameIsGenerated="true">
      <module name="master" />
      <working_directory value="$PROJECT_DIR$/utils" />
      <kind value="PACKAGE" />
      <package value="master/utils" />
      <directory value="$PROJECT_DIR$/" />
      <filePath value="$PROJECT_DIR$/" />
      <framework value="gotest" />
      <pattern value="^TestRandArr$" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="master" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Test.TestRandArr in master/utils" />
        <item itemvalue="Go Test.TestJsonMarshal in master/utils" />
        <item itemvalue="Go Test.TestMapCreation in master/utils" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <environment>
      <map>
        <entry key="GOPROXY" value="https://goproxy.cn,direct" />
      </map>
    </environment>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/utils/concurrent_map_test.go</url>
          <line>526</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/center/crossserver/mgr_champion.go</url>
          <line>1235</line>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/center/crossserver/mgr_champion.go</url>
          <line>823</line>
          <option name="timeStamp" value="50" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/center/crossserver/mgr_champion.go</url>
          <line>785</line>
          <option name="timeStamp" value="52" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>