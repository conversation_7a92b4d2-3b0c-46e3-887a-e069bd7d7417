package crossserver

import (
	"fmt"
	"master/center/server"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"sync"
	"time"

	json "github.com/bytedance/sonic"
)

const (
	MINE_WAR_REDIS_FIGHT_RECORD = "minewarfightrecord"
	MINE_WAR_REDIS_RECORD       = "minewarrecord"
	MINE_WAR_REDIS_FIGHT_ID     = "minewarfightid"
	MINE_WAR_REDIS_RECORD_NUM   = 20
	MINE_WAR_ATTACK_CD          = 90
	MINE_WAR_STATUS_PREPARE     = 1 // 准备期
	MINE_WAR_STATUS_BATTLE      = 2 // 战斗期
	MINE_WAR_STATUS_RESULT      = 3 // 结算期
)

const (
	ACTION_ENTER  = 1
	ACTION_LEAVE  = 2
	ACTION_ATTACK = 3

	// 挑战次数相关动作
	CHALLENGE_ACTION_QUERY = 1 // 查询挑战次数
	CHALLENGE_ACTION_BUY   = 2 // 购买挑战次数

	// 队伍解锁相关动作
	TEAM_UNLOCK_ACTION_QUERY  = 1 // 查询解锁状态
	TEAM_UNLOCK_ACTION_UNLOCK = 2 // 尝试解锁队伍

	// 护盾相关动作
	SHIELD_ACTION_ACTIVATE   = 1 // 激活护盾
	SHIELD_ACTION_DEACTIVATE = 2 // 取消护盾
	SHIELD_ACTION_QUERY      = 3 // 查询护盾状态

	// 护盾相关常量
	SHIELD_DEFAULT_DURATION = 3600 * 8 // 默认护盾持续时间(8小时)
	SHIELD_FREE_COUNT       = 1        // 每日免费护盾次数
	SHIELD_MAX_COUNT        = 5        // 每日最大护盾次数

	// 队伍解锁相关常量
	TEAM_INITIAL_COUNT    = 1  // 初始队伍数量
	TEAM_UNLOCK_CHALLENGE = 10 // 解锁多队伍需要的挑战次数
	TEAM_MAX_UNLOCK_COUNT = 3  // 最大可解锁队伍数量

	MINE_LEVEL_MAX = 7  // 矿点等级上限(1-7级)
	POSITION_MAX   = 16 // 坑位上限
	TEAM_MAX       = 3  // 队伍上限

	// 时间常量
	WEEKLY_START_DAY  = 2  // 每周开始日(周二)
	ACTIVITY_DURATION = 6  // 活动持续天数
	SETTLEMENT_HOUR   = 20 // 结算时间(20:00)
	SETTLEMENT_DAY    = 0  // 结算日(周日)
	REST_START_HOUR   = 23 // 休赛期开始时间(23:00)
	REST_END_HOUR     = 12 // 休赛期结束时间(12:00)

	// 矿脉等级常量
	MINE_LEVEL_PRIVATE_MAX = 3 // 私有矿脉最高等级(1-3级)
	MINE_LEVEL_PUBLIC_MIN  = 4 // 公共矿脉最低等级(4级)
	MINE_LEVEL_PUBLIC_MAX  = 7 // 公共矿脉最高等级(7级)

	// 产出效率
	REST_PERIOD_OUTPUT_RATE = 50 // 休赛期产出效率(50%)

	// 挑战次数相关
	DAILY_CHALLENGE_FREE_COUNT = 5  // 每日免费挑战次数
	DAILY_CHALLENGE_MAX_COUNT  = 10 // 每日最大挑战次数(包括购买)
	CHALLENGE_BUY_MAX_COUNT    = 5  // 每日最大购买次数

	// 矿脉战斗事件常量
	MINE_WAR_EVENT_GUILD_REWARD  = 12001 // 公会奖励事件
	MINE_WAR_EVENT_PLAYER_REWARD = 12002 // 个人奖励事件
	MINE_WAR_EVENT_SERVER_REWARD = 12003 // 服务器奖励事件
	MINE_WAR_EVENT_OCCUPY_NOTIFY = 12004 // 矿脉占领通知事件
	MINE_WAR_EVENT_ATTACK_NOTIFY = 12005 // 矿脉攻击通知事件

	// 矿脉战斗排行榜类型
	RANK_TYPE_MINE_WAR_PLAYER = 15 // 矿脉战斗个人积分排行
	RANK_TYPE_MINE_WAR_GUILD  = 16 // 矿脉战斗公会积分排行
	RANK_TYPE_MINE_WAR_SERVER = 17 // 矿脉战斗服务器积分排行
)

// 矿脉战斗配置扩展信息
type MineWarConfigInfo struct {
	KeepTime           int64 `json:"keep_time"`             // 持续时间
	BoothProtection    int64 `json:"booth_protection"`      // 占位保护时长秒
	AttackNum          int   `json:"attack_num"`            // 每日攻击次数
	ShieldProtection   int64 `json:"shield_protection"`     // 护盾保护时长秒
	BoothMaxtime       int64 `json:"booth_maxtime"`         // 坑位最长占领时间
	AttackCd           int64 `json:"attack_cd"`             // 队伍攻击CD时间
	LastSettlementWeek int64 `json:"last_settlement_week"`  // 上次结算的周数
	LastDailyResetTime int64 `json:"last_daily_reset_time"` // 上次每日重置时间
	// 预留扩展字段
	ExtData map[string]interface{} `json:"ext_data,omitempty"` // 扩展数据
}

type MineWarConfig struct {
	Id          int    // 主键ID
	KeyId       int    // 活动配置ID
	StartTime   int64  // 开始时间
	PrepareTime int64  // 准备期
	BattleTime  int64  // 战斗期
	ResultTime  int64  // 结算期
	EndTime     int64  // 总结束时间
	Info        string // 配置扩展信息JSON

	// 运行时字段(不存数据库)
	configInfo *MineWarConfigInfo `json:"-"`

	db.DataUpdate
}

type MineWarUpdateInfo struct {
	Point  int64   `json:"point"`
	TeamId []int64 `json:"team_id"`
}

// 确保configInfo已初始化
func (self *MineWarConfig) ensureConfigInfo() {
	if self.configInfo == nil {
		self.configInfo = &MineWarConfigInfo{
			KeepTime:         7 * 24 * 3600, // 默认7天
			BoothProtection:  300,           // 默认5分钟
			AttackNum:        10,            // 默认10次
			ShieldProtection: 8 * 3600,      // 默认8小时
			BoothMaxtime:     24 * 3600,     // 默认24小时
			AttackCd:         60,            // 默认1分钟
			ExtData:          make(map[string]interface{}),
		}
	}
}

func (self *MineWarConfig) Decode() {
	// 解析配置扩展信息
	if self.Info != "" {
		self.configInfo = &MineWarConfigInfo{}
		json.Unmarshal([]byte(self.Info), self.configInfo)
	} else {
		self.ensureConfigInfo()
	}
}

// ! 将data数据写入数据库
func (self *MineWarConfig) Encode() {
	// 编码配置扩展信息
	if self.configInfo != nil {
		self.Info = utils.HF_JtoA(&self.configInfo)
	}
}

// 矿脉等级配置表
type MineWarLevelConfig struct {
	Level          int   `json:"level"`            // 矿脉等级
	UnlockDay      int   `json:"unlock_day"`       // 解锁天数
	MaxAccumTime   int64 `json:"max_accum_time"`   // 最大累积时间(小时)
	BaseOutputRate int64 `json:"base_output_rate"` // 基础产出率(每秒)
	Shield         int64 `json:"shield"`           // 护盾值
	AttackCd       int64 `json:"attack_cd"`        // 攻击冷却时间(秒)
	MaxAttackNum   int   `json:"max_attack_num"`   // 每日最大攻击次数
	IsPublic       int   `json:"is_public"`        // 是否为公共矿脉(0:私有 1:公共)
	MineCount      int   `json:"mine_count"`       // 该等级矿脉数量
}

// 矿脉活动配置表
type MineWarActivityConfig struct {
	ActivityGroup        int `json:"activity_group"`          // 活动组ID
	KeepTime             int `json:"keep_time"`               // 活动持续天数
	StartDay             int `json:"start_day"`               // 开始日期(周几)
	SettlementHour       int `json:"settlement_hour"`         // 结算时间(小时)
	RestStartHour        int `json:"rest_start_hour"`         // 休赛期开始时间
	RestEndHour          int `json:"rest_end_hour"`           // 休赛期结束时间
	RestOutputRate       int `json:"rest_output_rate"`        // 休赛期产出效率(百分比)
	TeamMaxNum           int `json:"team_max_num"`            // 玩家最大队伍数量
	BonusRate            int `json:"bonus_rate"`              // 5级矿脉加成比例(百分比)
	ChallengeFreeCount   int `json:"challenge_free_count"`    // 每日免费挑战次数
	ChallengeMaxCount    int `json:"challenge_max_count"`     // 每日最大挑战次数
	ChallengeBuyMaxCount int `json:"challenge_buy_max_count"` // 每日最大购买次数
	ChallengeBuyCost     int `json:"challenge_buy_cost"`      // 购买挑战次数消耗货币数量
	ShieldFreeCount      int `json:"shield_free_count"`       // 每日免费护盾次数
	ShieldMaxCount       int `json:"shield_max_count"`        // 每日最大护盾次数
	ShieldBuyMaxCount    int `json:"shield_buy_max_count"`    // 每日最大购买护盾次数
	ShieldBuyCost        int `json:"shield_buy_cost"`         // 购买护盾消耗货币数量
	ShieldDuration       int `json:"shield_duration"`         // 护盾持续时间(小时)
	TeamInitialCount     int `json:"team_initial_count"`      // 初始队伍数量
	TeamUnlockChallenge  int `json:"team_unlock_challenge"`   // 解锁多队伍需要的挑战次数
	TeamMaxUnlockCount   int `json:"team_max_unlock_count"`   // 最大可解锁队伍数量
}

// 矿脉产出配置表
type MineWarOutputConfig struct {
	Level       int `json:"level"`        // 矿脉等级
	OutputItem1 int `json:"output_item1"` // 产出物品1ID(商店货币)
	OutputNum1  int `json:"output_num1"`  // 产出物品1数量
	OutputItem2 int `json:"output_item2"` // 产出物品2ID(治疗妖姬)
	OutputNum2  int `json:"output_num2"`  // 产出物品2数量
	OutputItem3 int `json:"output_item3"` // 产出物品3ID(积分)
	OutputNum3  int `json:"output_num3"`  // 产出物品3数量
}

type JS_MineInfo struct {
	MineId     int64 `json:"mine_id"`
	TeamType   int   `json:"team_type"`
	StartTime  int64 `json:"start_time"`  // 占领起始时间
	ShieldTime int64 `json:"shield_time"` // 护盾保护起始时间
}

// 矿脉扩展信息结构
type MineWarMineInfo struct {
	Level           int   `json:"level"`             // 矿脉等级
	ServerIds       []int `json:"server_ids"`        // 所属服务器ID列表(1-3级矿脉专属)
	Region          int   `json:"region"`            // 区域ID(1-4，对应四个角落)
	IsPublic        bool  `json:"is_public"`         // 是否为公共矿脉(4-7级)
	UnlockDay       int   `json:"unlock_day"`        // 解锁天数
	IsUnlocked      bool  `json:"is_unlocked"`       // 是否已解锁
	BonusActive     bool  `json:"bonus_active"`      // 是否有产出加成(5级矿脉随机)
	MaxAccumTime    int64 `json:"max_accum_time"`    // 最大累积时间(秒)
	ShieldActive    bool  `json:"shield_active"`     // 护盾是否激活
	ShieldStartTime int64 `json:"shield_start_time"` // 护盾开始时间
	ShieldEndTime   int64 `json:"shield_end_time"`   // 护盾结束时间
	ShieldDuration  int64 `json:"shield_duration"`   // 护盾持续时间(秒)
	// 预留字段，方便后续扩展
	ExtData map[string]interface{} `json:"ext_data,omitempty"` // 扩展数据
}

type MineWarMine struct {
	MineId     int64  // 矿点ID
	GroupId    int    // 预留字段 暂不启用 如果以后要做服务器分组的话不用额外加字段了
	SeasonTime int64  // 赛季时间
	FightInfo  string // 战斗信息JSON
	StartTime  int64  // 占领起始时间
	AttackTime int64  // 攻击者的攻击时间
	AttackUid  int64  // 攻击者的uid
	ShieldTime int64  // 护盾保护起始时间(兼容旧字段)
	Info       string // 矿脉扩展信息JSON

	// 运行时字段(不存数据库)
	fightInfo *model.JS_FightInfo `json:"-"`
	mineInfo  *MineWarMineInfo    `json:"-"`

	db.DataUpdate
}

// ! 将数据库数据写入data-无特殊结构
func (self *MineWarMine) Decode() {
	// 解析战斗信息
	if self.FightInfo != "" {
		json.Unmarshal([]byte(self.FightInfo), &self.fightInfo)
	}

	// 解析矿脉扩展信息
	if self.Info != "" {
		self.mineInfo = &MineWarMineInfo{}
		json.Unmarshal([]byte(self.Info), self.mineInfo)
	} else {
		// 如果没有Info数据，创建默认的
		self.mineInfo = &MineWarMineInfo{
			Level:        1,
			ServerIds:    []int{},
			Region:       0,
			IsPublic:     false,
			IsUnlocked:   false,
			BonusActive:  false,
			MaxAccumTime: 3600 * 24, // 默认24小时
			ExtData:      make(map[string]interface{}),
		}
	}
}

// ! 将data数据写入数据库
func (self *MineWarMine) Encode() {
	// 编码战斗信息
	if self.fightInfo != nil {
		self.FightInfo = utils.HF_JtoA(&self.fightInfo)
	}

	// 编码矿脉扩展信息
	if self.mineInfo != nil {
		self.Info = utils.HF_JtoA(&self.mineInfo)
	}
}

// 确保mineInfo已初始化
func (self *MineWarMine) ensureMineInfo() {
	if self.mineInfo == nil {
		self.mineInfo = &MineWarMineInfo{
			Level:        1,
			ServerIds:    []int{},
			Region:       0,
			IsPublic:     false,
			IsUnlocked:   false,
			BonusActive:  false,
			MaxAccumTime: 3600 * 24, // 默认24小时
			ExtData:      make(map[string]interface{}),
		}
	}
}

// 设置服务器ID列表
func (self *MineWarMine) SetServerIds(serverIds []int) {
	self.ensureMineInfo()
	self.mineInfo.ServerIds = serverIds
}

// 添加服务器ID
func (self *MineWarMine) AddServerId(serverId int) {
	self.ensureMineInfo()
	if !self.HasServerId(serverId) {
		self.mineInfo.ServerIds = append(self.mineInfo.ServerIds, serverId)
	}
}

// 检查是否包含指定服务器ID
func (self *MineWarMine) HasServerId(serverId int) bool {
	self.ensureMineInfo()
	for _, id := range self.mineInfo.ServerIds {
		if id == serverId {
			return true
		}
	}
	return false
}

// 获取服务器ID列表
func (self *MineWarMine) GetServerIds() []int {
	self.ensureMineInfo()
	return self.mineInfo.ServerIds
}

// 获取第一个服务器ID（兼容旧代码）
func (self *MineWarMine) GetFirstServerId() int {
	self.ensureMineInfo()
	if len(self.mineInfo.ServerIds) > 0 {
		return self.mineInfo.ServerIds[0]
	}
	return 0
}

// 获取矿脉信息（确保初始化）
func (self *MineWarMine) GetMineInfo() *MineWarMineInfo {
	self.ensureMineInfo()
	return self.mineInfo
}

type MineWarServer struct {
	ServerId   int    `json:"server_id"`
	ServerName string `json:"server_name"`
}

// 矿脉战斗玩家扩展信息
type MineWarPlayerInfo struct {
	TeamMine            map[int64]int64 `json:"team_mine"`             // map[队伍id][矿点id]
	ChallengeCount      int             `json:"challenge_count"`       // 今日已使用挑战次数
	ChallengeBuyCount   int             `json:"challenge_buy_count"`   // 今日已购买挑战次数
	ShieldCount         int             `json:"shield_count"`          // 今日已使用护盾次数
	ShieldBuyCount      int             `json:"shield_buy_count"`      // 今日已购买护盾次数
	LastResetTime       int64           `json:"last_reset_time"`       // 上次重置时间(用于判断是否需要重置)
	TotalChallengeCount int             `json:"total_challenge_count"` // 累计挑战次数(不重置)
	UnlockedTeamCount   int             `json:"unlocked_team_count"`   // 已解锁的队伍数量
	// 预留扩展字段
	ExtData map[string]interface{} `json:"ext_data,omitempty"` // 扩展数据
}

type MineWarPlayer struct {
	Uid      int64  // 玩家UID
	ServerId int    // 服务器ID
	Info     string // 玩家扩展信息JSON

	// 运行时字段(不存数据库)
	playerInfo *MineWarPlayerInfo `json:"-"`

	db.DataUpdate
}

// 确保playerInfo已初始化
func (self *MineWarPlayer) ensurePlayerInfo() {
	if self.playerInfo == nil {
		self.playerInfo = &MineWarPlayerInfo{
			TeamMine:            make(map[int64]int64),
			ChallengeCount:      0,
			ChallengeBuyCount:   0,
			ShieldCount:         0,
			ShieldBuyCount:      0,
			LastResetTime:       0,
			TotalChallengeCount: 0,
			UnlockedTeamCount:   TEAM_INITIAL_COUNT, // 初始1队
			ExtData:             make(map[string]interface{}),
		}
	}
}

// ! 将数据库数据写入data-无特殊结构
func (self *MineWarPlayer) Decode() {
	// 解析玩家扩展信息
	if self.Info != "" {
		self.playerInfo = &MineWarPlayerInfo{}
		json.Unmarshal([]byte(self.Info), self.playerInfo)
	} else {
		self.ensurePlayerInfo()
	}
}

// ! 将data数据写入数据库
func (self *MineWarPlayer) Encode() {
	// 编码玩家扩展信息
	if self.playerInfo != nil {
		self.Info = utils.HF_JtoA(&self.playerInfo)
	}
}

// 获取玩家信息（确保初始化）
func (self *MineWarPlayer) GetPlayerInfo() *MineWarPlayerInfo {
	self.ensurePlayerInfo()
	return self.playerInfo
}

func (self *MineWarInfo) JS_MineInfoChange(mine *MineWarMine) *JS_MineInfo {
	mineInfo := new(JS_MineInfo)
	mineInfo.MineId = mine.MineId
	mineInfo.TeamType = int(mine.fightInfo.Param)
	mineInfo.StartTime = mine.StartTime
	mineInfo.ShieldTime = mine.ShieldTime
	return mineInfo
}

type RPC_MinePointReq struct {
	MineId      int64 `json:"mine_id"`
	Uid         int64 `json:"uid"`
	ServerId    int64 `json:"server_id"`
	TeamId      int64 `json:"team_id"`
	Position    int   `json:"position"`
	AttackPower int64 `json:"attack_power"`
	GroupId     int   `json:"group_id"` // 支线ID
}

type RPC_MinePointRes struct {
	RetCode           int              `json:"ret_code"`
	Points            []*MinePoint     `json:"points"`
	Positions         []*MinePosition  `json:"positions"`
	Teams             []*MineTeam      `json:"teams"`
	OutputNum         int              `json:"output_num"`
	OutputItem        int              `json:"output_item"`
	OutputItems       []MineOutputItem `json:"output_items"`        // 多种产出物品
	UnlockedTeamCount int              `json:"unlocked_team_count"` // 已解锁队伍数量
	MaxTeamCount      int              `json:"max_team_count"`      // 最大队伍数量
}

// 挑战次数相关RPC
type RPC_MineChallengeReq struct {
	Uid     int64 `json:"uid"`
	GroupId int   `json:"group_id"`
	Action  int   `json:"action"` // 1:查询次数 2:购买次数
}

type RPC_MineChallengeRes struct {
	RetCode           int `json:"ret_code"`
	RemainingCount    int `json:"remaining_count"`     // 剩余挑战次数
	UsedCount         int `json:"used_count"`          // 已使用挑战次数
	RemainingBuyCount int `json:"remaining_buy_count"` // 剩余购买次数
	UsedBuyCount      int `json:"used_buy_count"`      // 已购买次数
	BuyCost           int `json:"buy_cost"`            // 购买消耗
}

// 护盾相关RPC
type RPC_MineShieldReq struct {
	Uid     int64 `json:"uid"`
	GroupId int   `json:"group_id"`
	MineId  int64 `json:"mine_id"`
	Action  int   `json:"action"` // 1:激活护盾 2:取消护盾 3:查询状态 4:购买护盾次数
}

type RPC_MineShieldRes struct {
	RetCode           int   `json:"ret_code"`
	ShieldActive      bool  `json:"shield_active"`       // 护盾是否激活
	ShieldStartTime   int64 `json:"shield_start_time"`   // 护盾开始时间
	ShieldEndTime     int64 `json:"shield_end_time"`     // 护盾结束时间
	ShieldDuration    int64 `json:"shield_duration"`     // 护盾持续时间(秒)
	RemainingTime     int64 `json:"remaining_time"`      // 护盾剩余时间(秒)
	RemainingCount    int   `json:"remaining_count"`     // 剩余护盾次数
	UsedCount         int   `json:"used_count"`          // 已使用护盾次数
	RemainingBuyCount int   `json:"remaining_buy_count"` // 剩余购买次数
	UsedBuyCount      int   `json:"used_buy_count"`      // 已购买次数
	BuyCost           int   `json:"buy_cost"`            // 购买消耗
}

// 队伍解锁相关RPC
type RPC_MineTeamUnlockReq struct {
	Uid     int64 `json:"uid"`
	GroupId int   `json:"group_id"`
	Action  int   `json:"action"` // 1:查询解锁状态 2:尝试解锁队伍
}

type RPC_MineTeamUnlockRes struct {
	RetCode             int  `json:"ret_code"`
	TotalChallengeCount int  `json:"total_challenge_count"` // 累计挑战次数
	UnlockedTeamCount   int  `json:"unlocked_team_count"`   // 已解锁队伍数量
	MaxTeamCount        int  `json:"max_team_count"`        // 最大队伍数量
	NextUnlockChallenge int  `json:"next_unlock_challenge"` // 下次解锁需要的挑战次数
	CanUnlock           bool `json:"can_unlock"`            // 是否可以解锁新队伍
}

// 积分查询相关RPC
type RPC_MineScoreReq struct {
	Uid     int64 `json:"uid"`
	GroupId int   `json:"group_id"`
	GuildId int64 `json:"guild_id"`
	Action  int   `json:"action"` // 1:查询个人积分 2:查询公会积分 3:查询服务器积分
}

type RPC_MineScoreRes struct {
	RetCode     int   `json:"ret_code"`
	PlayerScore int64 `json:"player_score"` // 个人积分
	GuildScore  int64 `json:"guild_score"`  // 公会积分
	ServerScore int64 `json:"server_score"` // 服务器积分
}

type MinePoint struct {
	MineId       int64 `json:"mine_id"`
	OwnerId      int64 `json:"owner_id"`
	Shield       int64 `json:"shield"`
	AttackNum    int   `json:"attack_num"`
	OutputItem   int   `json:"output_item"`
	OutputRate   int64 `json:"output_rate"`
	Level        int   `json:"level"`
	LineIndex    int   `json:"line_index"`
	GroupId      int   `json:"group_id"`       // 支线ID，0表示公共矿点
	ServerId     int   `json:"server_id"`      // 所属服务器ID(1-3级矿脉专属)
	Region       int   `json:"region"`         // 区域ID(1-4，对应四个角落)
	IsPublic     bool  `json:"is_public"`      // 是否为公共矿脉(4-7级)
	UnlockDay    int   `json:"unlock_day"`     // 解锁天数
	IsUnlocked   bool  `json:"is_unlocked"`    // 是否已解锁
	BonusActive  bool  `json:"bonus_active"`   // 是否有产出加成(5级矿脉随机)
	MaxAccumTime int64 `json:"max_accum_time"` // 最大累积时间(秒)
	LastSaveTime int64 `json:"last_save_time"`
	OutputNum    int64 `json:"output_num"`
}

type MinePosition struct {
	PositionId int64 `json:"position_id"`
	MineId     int64 `json:"mine_id"`
	PlayerId   int64 `json:"player_id"`
	EnterTime  int64 `json:"enter_time"`
	LeaveTime  int64 `json:"leave_time"`
	Status     int   `json:"status"`
	GroupId    int   `json:"group_id"`
}

type MineTeam struct {
	TeamId         int64 `json:"team_id"`
	PlayerId       int64 `json:"player_id"`
	MineId         int64 `json:"mine_id"`
	PositionId     int64 `json:"position_id"`
	GroupId        int   `json:"group_id"`
	Status         int   `json:"status"`
	LastAttackTime int64 `json:"last_attack_time"`
}

type MineWarInfo struct {
	MineWarMines       map[int]map[int64]*MineWarMine   // 矿点数据 map[group][mineid]
	MineWarPlayer      map[int]map[int64]*MineWarPlayer // 玩家数据 map[group][uid]
	MineWarServer      map[int]map[int]*MineWarServer   // 参与的服务器数据 map[group][serverid]
	MineWarServerGroup map[int]int                      // 服务器所在的组 map[serverid]group

	MineWarConfig *MineWarConfig
	lock          *sync.RWMutex
}

// ! 矿战管理类
type MineWarMgr struct {
	MineWarInfo *MineWarInfo //!

	MineWarConfigMap         map[int64]*MineWarConfig       //Mine_War_Config
	MineWarLevelConfigMap    map[int]*MineWarLevelConfig    //Mine_War_Level_Config
	MineWarActivityConfigMap map[int]*MineWarActivityConfig //Mine_War_Activity_Config
	MineWarOutputConfigMap   map[int]*MineWarOutputConfig   //Mine_War_Output_Config
}

func (self *MineWarMgr) Save() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.Save()
	}
}

func (self *MineWarInfo) Save() {
	self.lock.RLock()
	defer self.lock.RUnlock()

	// 保存矿脉数据
	for _, group := range self.MineWarMines {
		for _, v := range group {
			v.Encode()
			v.UpdateEx("groupid", v.GroupId)
		}
	}

	// 保存玩家数据(包括挑战次数)
	for _, group := range self.MineWarPlayer {
		for _, player := range group {
			// TODO: 保存玩家挑战次数数据到数据库
			// 这里需要根据实际的数据库表结构来实现
			_ = player // 避免未使用变量警告
		}
	}

	self.MineWarConfig.Update(true, false)
}

func (self *MineWarInfo) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 检查是否在活动期间
	if !GetMineWarMgr().IsActivityPeriod() {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查挑战次数是否足够
	if !self.CheckPlayerChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查玩家队伍数量是否已满
	if !self.CanPlayerAddTeam(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查玩家是否可以占领新的矿脉
	if !self.CanPlayerOccupyMine(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否可以攻击该矿脉
	if !self.CanAttackMine(int(req.ServerId), mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 消耗挑战次数
	if !self.ConsumeChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// TODO: 实现具体的进入矿点逻辑

	// 返回队伍解锁信息
	player := self.GetOrCreateMineWarPlayer(req.Uid, req.GroupId)
	playerInfo := player.GetPlayerInfo()
	res.UnlockedTeamCount = playerInfo.UnlockedTeamCount

	// 获取最大队伍数量
	maxTeamCount := TEAM_MAX_UNLOCK_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxTeamCount = activityConfig.TeamMaxUnlockCount
		}
	}
	res.MaxTeamCount = maxTeamCount

	res.RetCode = RETCODE_OK
}

// 检查玩家是否可以添加新队伍
func (self *MineWarInfo) CanPlayerAddTeam(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 确保队伍解锁状态是最新的
	self.CheckTeamUnlock(player)

	// 统计玩家当前的队伍数量
	playerInfo := player.GetPlayerInfo()
	teamCount := len(playerInfo.TeamMine)

	// 检查是否超过已解锁的队伍数量
	return teamCount < playerInfo.UnlockedTeamCount
}

// 获取玩家可占领的矿脉数量限制
func (self *MineWarInfo) GetPlayerMineLimit(uid int64, groupId int) int {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 确保队伍解锁状态是最新的
	self.CheckTeamUnlock(player)

	// 返回已解锁的队伍数量，即可同时占领的矿脉数量
	playerInfo := player.GetPlayerInfo()
	return playerInfo.UnlockedTeamCount
}

// 检查玩家是否可以占领新的矿脉
func (self *MineWarInfo) CanPlayerOccupyMine(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 确保队伍解锁状态是最新的
	self.CheckTeamUnlock(player)

	// 统计玩家当前占领的矿脉数量
	occupiedCount := 0
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil && mine.fightInfo.Uid == uid {
				occupiedCount++
			}
		}
	}

	// 检查是否超过解锁的队伍数量限制
	playerInfo := player.GetPlayerInfo()
	return occupiedCount < playerInfo.UnlockedTeamCount
}

// 获取矿脉信息
func (self *MineWarInfo) GetMineWarMine(groupId int, mineId int64) (*MineWarMine, bool) {
	if group, ok := self.MineWarMines[groupId]; ok {
		if mine, ok := group[mineId]; ok {
			return mine, true
		}
	}
	return nil, false
}

// 检查队伍是否可以更换阵容
func (self *MineWarInfo) CanChangeTeamFormation(uid int64, teamId int64) bool {
	// 检查队伍是否正在占据矿脉
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil && mine.fightInfo.Uid == uid && mine.fightInfo.Param == teamId {
				return false // 正在占据矿脉，无法更换阵容
			}
		}
	}
	return true
}

func (self *MineWarInfo) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 检查是否在战斗时间
	if !GetMineWarMgr().IsWarTime() {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查挑战次数是否足够
	if !self.CheckPlayerChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否可以攻击该矿脉
	if !self.CanAttackMine(int(req.ServerId), mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查矿脉是否有护盾保护
	if self.IsShieldActive(mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 消耗挑战次数
	if !self.ConsumeChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// TODO: 实现具体的攻击逻辑
	// 这里应该包括战斗计算、矿脉占领状态更新等

	// 模拟战斗结果（实际应该根据战斗系统计算）
	attackResult := 1 // 1:攻击成功 0:攻击失败

	// 记录原占领者
	defenderId := int64(0)
	if mine.fightInfo != nil {
		defenderId = mine.fightInfo.Uid
	}

	// 发送攻击通知
	self.SendMineAttackNotify(mine, req.Uid, defenderId, attackResult)

	// 如果攻击成功并占领矿脉，需要取消原有的护盾
	if attackResult == 1 {
		self.OnMineOccupied(mine, req.Uid)
	}

	res.RetCode = RETCODE_OK
}

// 矿脉被占领时的处理
func (self *MineWarInfo) OnMineOccupied(mine *MineWarMine, newOwnerId int64) {
	// 记录原占领者
	oldOwnerId := int64(0)
	if mine.fightInfo != nil {
		oldOwnerId = mine.fightInfo.Uid
	}

	// 取消护盾
	mineInfo := mine.GetMineInfo()
	if mineInfo.ShieldActive {
		mineInfo.ShieldActive = false
		mineInfo.ShieldStartTime = 0
		mineInfo.ShieldEndTime = 0
		mineInfo.ShieldDuration = 0
	}

	// 更新占领者信息
	if mine.fightInfo != nil {
		mine.fightInfo.Uid = newOwnerId
	}

	// 重置开始时间用于产出计算
	mine.StartTime = time.Now().Unix()

	// 发送占领通知
	self.SendMineOccupyNotify(mine, newOwnerId, oldOwnerId)
}

func (self *MineWarInfo) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// TODO: 实现离开矿点逻辑
	res.RetCode = RETCODE_OK
}

func (self *MineWarInfo) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否为占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != req.Uid {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 计算产出
	currentTime := time.Now().Unix()
	outputTime := currentTime - mine.StartTime

	mineInfo := mine.GetMineInfo()

	// 限制最大累积时间
	if outputTime > mineInfo.MaxAccumTime {
		outputTime = mineInfo.MaxAccumTime
	}

	// 计算基础产出
	baseRate := self.GetMineBaseOutputRate(mineInfo.Level)
	actualRate := self.GetOutputRate(baseRate)

	// 如果有加成，增加产出
	if mineInfo.BonusActive {
		bonusRate := 150 // 默认50%加成
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				bonusRate = 100 + activityConfig.BonusRate
			}
		}
		actualRate = actualRate * int64(bonusRate) / 100
	}

	// 护盾期间稳定收益：护盾激活期间产出效率提升
	if self.IsShieldActive(mine) {
		// 护盾期间产出效率提升20%
		actualRate = actualRate * 120 / 100
	}

	totalOutput := outputTime * actualRate

	// 更新时间
	mine.StartTime = currentTime

	// 发送产出奖励到玩家
	if totalOutput > 0 && mine.fightInfo != nil {
		outputItems := self.GetMineOutputItems(mineInfo.Level)
		// 根据实际产出调整数量
		for i := range outputItems {
			outputItems[i].ItemNum = int(totalOutput * int64(outputItems[i].ItemNum) / 100)
		}
		self.SendMineOutputReward(mine.fightInfo.Uid, mine.MineId, outputItems)
	}

	res.RetCode = RETCODE_OK
	res.OutputNum = int(totalOutput)
	res.OutputItems = self.GetMineOutputItems(mineInfo.Level)
}

// 获取矿脉基础产出率
func (self *MineWarInfo) GetMineBaseOutputRate(level int) int64 {
	config := GetMineWarMgr().GetMineWarLevelConfig(level)
	if config != nil {
		return config.BaseOutputRate
	}

	// 如果没有配置，使用默认值
	switch level {
	case 1:
		return 10 // 每秒10个货币
	case 2:
		return 15
	case 3:
		return 20
	case 4:
		return 30
	case 5:
		return 45
	case 6:
		return 60
	case 7:
		return 100
	default:
		return 10
	}
}

// 获取矿脉产出配置
func (self *MineWarInfo) GetMineOutputItems(level int) []MineOutputItem {
	config := GetMineWarMgr().GetMineWarOutputConfig(level)
	if config != nil {
		items := make([]MineOutputItem, 0)
		if config.OutputItem1 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem1, ItemNum: config.OutputNum1})
		}
		if config.OutputItem2 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem2, ItemNum: config.OutputNum2})
		}
		if config.OutputItem3 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem3, ItemNum: config.OutputNum3})
		}
		return items
	}

	// 如果没有配置，返回默认产出
	return []MineOutputItem{
		{ItemId: 10001 + level, ItemNum: 1}, // 示例物品ID
	}
}

// 矿脉产出物品结构
type MineOutputItem struct {
	ItemId  int `json:"item_id"`
	ItemNum int `json:"item_num"`
}

// 更新玩家积分
func (self *MineWarInfo) UpdatePlayerScore(uid int64, serverId int, score int64, fightInfo *model.JS_FightInfo) {
	if uid <= 0 || score <= 0 || fightInfo == nil {
		return
	}

	groupId := self.GetGroupId(serverId, int(self.MineWarConfig.KeyId))

	// 获取当前个人积分并累加
	currentPlayerScore := int64(0)
	playerRankInfo := GetRankMgr().GetMyRank(RANK_TYPE_MINE_WAR_PLAYER, groupId, uid)
	if playerRankInfo != nil {
		currentPlayerScore = playerRankInfo.Num
	}

	// 更新个人积分排行
	rankInfo := &model.RankInfo{
		Uid:       uid,
		RankId:    RANK_TYPE_MINE_WAR_PLAYER,
		Period:    groupId,
		SvrId:     serverId,
		SvrName:   server.GetServerMgr().GetServerName(serverId),
		UName:     fightInfo.Uname,
		Level:     fightInfo.Level,
		Vip:       fightInfo.Vip,
		Icon:      fightInfo.Iconid,
		Portrait:  fightInfo.Portrait,
		Fight:     fightInfo.Deffight,
		FameId:    fightInfo.FameId,
		Camp:      fightInfo.Camp,
		UnionName: fightInfo.UnionName,
		Time:      model.TimeServer().Unix(),
		Num:       currentPlayerScore + score, // 累加积分
	}

	msg := &RPC_UploadRankReq{
		ServerId: serverId,
		RankId:   RANK_TYPE_MINE_WAR_PLAYER,
		RankInfo: rankInfo,
	}
	GetRankMgr().UploadRank(msg, nil)

	// 更新公会积分（如果有公会）
	if fightInfo.UnionName != "" && fightInfo.UnionId > 0 {
		self.UpdateGuildScore(int64(fightInfo.UnionId), serverId, score, fightInfo, groupId)
	}

	// 更新服务器积分
	self.UpdateServerScore(serverId, score, groupId)
}

// 更新公会积分
func (self *MineWarInfo) UpdateGuildScore(guildId int64, serverId int, score int64, fightInfo *model.JS_FightInfo, groupId int) {
	// 获取当前公会积分
	currentGuildScore := int64(0)
	guildRankInfo := GetRankMgr().GetMyRank(RANK_TYPE_MINE_WAR_GUILD, groupId, guildId)
	if guildRankInfo != nil {
		currentGuildScore = guildRankInfo.Num
	}

	// 更新公会积分排行
	rankInfo := &model.RankInfo{
		Uid:       guildId,
		RankId:    RANK_TYPE_MINE_WAR_GUILD,
		Period:    groupId,
		SvrId:     serverId,
		SvrName:   server.GetServerMgr().GetServerName(serverId),
		UName:     fightInfo.UnionName,
		Level:     0,
		Vip:       0,
		Icon:      0,
		Portrait:  0,
		Fight:     0,
		FameId:    0,
		Camp:      0,
		UnionName: fightInfo.UnionName,
		Time:      model.TimeServer().Unix(),
		Num:       currentGuildScore + score,
	}

	msg := &RPC_UploadRankReq{
		ServerId: serverId,
		RankId:   RANK_TYPE_MINE_WAR_GUILD,
		RankInfo: rankInfo,
	}
	GetRankMgr().UploadRank(msg, nil)
}

// 更新服务器积分
func (self *MineWarInfo) UpdateServerScore(serverId int, score int64, groupId int) {
	// 获取当前服务器积分
	currentServerScore := int64(0)
	serverRankInfo := GetRankMgr().GetMyRank(RANK_TYPE_MINE_WAR_SERVER, groupId, int64(serverId))
	if serverRankInfo != nil {
		currentServerScore = serverRankInfo.Num
	}

	// 更新服务器积分排行
	rankInfo := &model.RankInfo{
		Uid:       int64(serverId),
		RankId:    RANK_TYPE_MINE_WAR_SERVER,
		Period:    groupId,
		SvrId:     serverId,
		SvrName:   server.GetServerMgr().GetServerName(serverId),
		UName:     server.GetServerMgr().GetServerName(serverId),
		Level:     0,
		Vip:       0,
		Icon:      0,
		Portrait:  0,
		Fight:     0,
		FameId:    0,
		Camp:      0,
		UnionName: "",
		Time:      model.TimeServer().Unix(),
		Num:       currentServerScore + score,
	}

	msg := &RPC_UploadRankReq{
		ServerId: serverId,
		RankId:   RANK_TYPE_MINE_WAR_SERVER,
		RankInfo: rankInfo,
	}
	GetRankMgr().UploadRank(msg, nil)
}

// 检查玩家挑战次数是否足够
func (self *MineWarInfo) CheckPlayerChallengeCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数(每日0点重置)
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	maxCount := DAILY_CHALLENGE_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ChallengeMaxCount
		}
	}

	playerInfo := player.GetPlayerInfo()
	return playerInfo.ChallengeCount < maxCount
}

// 消耗玩家挑战次数
func (self *MineWarInfo) ConsumeChallengeCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	maxCount := DAILY_CHALLENGE_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ChallengeMaxCount
		}
	}

	playerInfo := player.GetPlayerInfo()
	if playerInfo.ChallengeCount >= maxCount {
		return false
	}

	playerInfo.ChallengeCount++
	playerInfo.TotalChallengeCount++ // 增加累计挑战次数

	// 检查是否可以解锁新队伍
	self.CheckTeamUnlock(player)

	return true
}

// 检查队伍解锁
func (self *MineWarInfo) CheckTeamUnlock(player *MineWarPlayer) {
	// 获取配置
	unlockChallenge := TEAM_UNLOCK_CHALLENGE
	maxTeamCount := TEAM_MAX_UNLOCK_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			unlockChallenge = activityConfig.TeamUnlockChallenge
			maxTeamCount = activityConfig.TeamMaxUnlockCount
		}
	}

	playerInfo := player.GetPlayerInfo()

	// 计算应该解锁的队伍数量
	shouldUnlockCount := 1 // 初始1队
	if playerInfo.TotalChallengeCount >= unlockChallenge {
		// 每达到解锁要求的挑战次数，解锁一个新队伍
		additionalTeams := (playerInfo.TotalChallengeCount-unlockChallenge)/unlockChallenge + 1
		shouldUnlockCount = 1 + additionalTeams
		if shouldUnlockCount > maxTeamCount {
			shouldUnlockCount = maxTeamCount
		}
	}

	// 更新解锁的队伍数量
	if shouldUnlockCount > playerInfo.UnlockedTeamCount {
		playerInfo.UnlockedTeamCount = shouldUnlockCount
	}
}

// 购买挑战次数
func (self *MineWarInfo) BuyChallengeCount(uid int64, groupId int) (bool, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	buyMaxCount := CHALLENGE_BUY_MAX_COUNT
	buyCost := 100 // 默认消耗100货币
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			buyMaxCount = activityConfig.ChallengeBuyMaxCount
			buyCost = activityConfig.ChallengeBuyCost
		}
	}

	playerInfo := player.GetPlayerInfo()

	// 检查是否已达到购买上限
	if playerInfo.ChallengeBuyCount >= buyMaxCount {
		return false, 0
	}

	playerInfo.ChallengeBuyCount++
	return true, buyCost
}

// 检查并重置每日挑战次数
func (self *MineWarInfo) CheckAndResetDailyChallengeCount(player *MineWarPlayer) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayUnix := today.Unix()

	playerInfo := player.GetPlayerInfo()

	// 如果上次重置时间不是今天，则重置挑战次数和护盾次数
	if playerInfo.LastResetTime < todayUnix {
		playerInfo.ChallengeCount = 0
		playerInfo.ChallengeBuyCount = 0
		playerInfo.ShieldCount = 0
		playerInfo.ShieldBuyCount = 0
		playerInfo.LastResetTime = todayUnix
	}
}

// 获取或创建矿战玩家数据
func (self *MineWarInfo) GetOrCreateMineWarPlayer(uid int64, groupId int) *MineWarPlayer {
	// 确保组存在
	if _, ok := self.MineWarPlayer[groupId]; !ok {
		self.MineWarPlayer[groupId] = make(map[int64]*MineWarPlayer)
	}

	// 获取或创建玩家数据
	if player, ok := self.MineWarPlayer[groupId][uid]; ok {
		return player
	}

	// 创建新的玩家数据
	player := &MineWarPlayer{
		Uid:      uid,
		ServerId: 0,  // 需要从其他地方获取
		Info:     "", // 初始为空，会在Decode时初始化
	}

	// 初始化玩家信息
	player.GetPlayerInfo() // 这会触发ensurePlayerInfo，创建默认的playerInfo

	self.MineWarPlayer[groupId][uid] = player
	return player
}

// 获取玩家剩余挑战次数信息
func (self *MineWarInfo) GetPlayerChallengeInfo(uid int64, groupId int) (int, int, int, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	maxCount := DAILY_CHALLENGE_MAX_COUNT
	buyMaxCount := CHALLENGE_BUY_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ChallengeMaxCount
			buyMaxCount = activityConfig.ChallengeBuyMaxCount
		}
	}

	playerInfo := player.GetPlayerInfo()

	remainingCount := maxCount - playerInfo.ChallengeCount
	if remainingCount < 0 {
		remainingCount = 0
	}

	remainingBuyCount := buyMaxCount - playerInfo.ChallengeBuyCount
	if remainingBuyCount < 0 {
		remainingBuyCount = 0
	}

	return remainingCount, playerInfo.ChallengeCount, remainingBuyCount, playerInfo.ChallengeBuyCount
}

// 检查矿脉是否有护盾保护
func (self *MineWarInfo) IsShieldActive(mine *MineWarMine) bool {
	mineInfo := mine.GetMineInfo()
	if !mineInfo.ShieldActive {
		return false
	}

	currentTime := time.Now().Unix()
	if currentTime > mineInfo.ShieldEndTime {
		// 护盾已过期，自动取消
		mineInfo.ShieldActive = false
		mineInfo.ShieldStartTime = 0
		mineInfo.ShieldEndTime = 0
		return false
	}

	return true
}

// 激活矿脉护盾
func (self *MineWarInfo) ActivateShield(uid int64, groupId int, mineId int64) bool {
	// 检查玩家护盾次数
	if !self.CheckPlayerShieldCount(uid, groupId) {
		return false
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(groupId, mineId)
	if !ok {
		return false
	}

	// 检查是否为矿脉占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != uid {
		return false
	}

	// 检查护盾是否已经激活
	if self.IsShieldActive(mine) {
		return false
	}

	// 获取护盾持续时间
	duration := int64(SHIELD_DEFAULT_DURATION)
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			duration = int64(activityConfig.ShieldDuration) * utils.HOUR_SECS
		}
	}

	// 激活护盾
	mineInfo := mine.GetMineInfo()
	currentTime := time.Now().Unix()
	mineInfo.ShieldActive = true
	mineInfo.ShieldStartTime = currentTime
	mineInfo.ShieldEndTime = currentTime + duration
	mineInfo.ShieldDuration = duration

	// 消耗护盾次数
	self.ConsumeShieldCount(uid, groupId)

	return true
}

// 取消矿脉护盾
func (self *MineWarInfo) DeactivateShield(uid int64, groupId int, mineId int64) bool {
	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(groupId, mineId)
	if !ok {
		return false
	}

	// 检查是否为矿脉占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != uid {
		return false
	}

	// 检查护盾是否激活
	mineInfo := mine.GetMineInfo()
	if !mineInfo.ShieldActive {
		return false
	}

	// 取消护盾
	mineInfo.ShieldActive = false
	mineInfo.ShieldStartTime = 0
	mineInfo.ShieldEndTime = 0
	mineInfo.ShieldDuration = 0

	return true
}

// 检查玩家护盾次数是否足够
func (self *MineWarInfo) CheckPlayerShieldCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	maxCount := SHIELD_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ShieldMaxCount
		}
	}

	playerInfo := player.GetPlayerInfo()
	return playerInfo.ShieldCount < maxCount
}

// 消耗玩家护盾次数
func (self *MineWarInfo) ConsumeShieldCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	maxCount := SHIELD_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ShieldMaxCount
		}
	}

	playerInfo := player.GetPlayerInfo()
	if playerInfo.ShieldCount >= maxCount {
		return false
	}

	playerInfo.ShieldCount++
	return true
}

// 购买护盾次数
func (self *MineWarInfo) BuyShieldCount(uid int64, groupId int) (bool, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	buyMaxCount := SHIELD_MAX_COUNT
	buyCost := 200 // 默认消耗200货币
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			buyMaxCount = activityConfig.ShieldBuyMaxCount
			buyCost = activityConfig.ShieldBuyCost
		}
	}

	playerInfo := player.GetPlayerInfo()

	// 检查是否已达到购买上限
	if playerInfo.ShieldBuyCount >= buyMaxCount {
		return false, 0
	}

	playerInfo.ShieldBuyCount++
	return true, buyCost
}

// 检查并重置每日护盾次数
func (self *MineWarInfo) CheckAndResetDailyShieldCount(player *MineWarPlayer) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayUnix := today.Unix()

	playerInfo := player.GetPlayerInfo()

	// 如果上次重置时间不是今天，则重置护盾次数
	if playerInfo.LastResetTime < todayUnix {
		playerInfo.ShieldCount = 0
		playerInfo.ShieldBuyCount = 0
		// 注意：这里不重置LastResetTime，因为挑战次数重置时会设置
	}
}

// 获取玩家护盾次数信息
func (self *MineWarInfo) GetPlayerShieldInfo(uid int64, groupId int) (int, int, int, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	maxCount := SHIELD_MAX_COUNT
	buyMaxCount := SHIELD_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ShieldMaxCount
			buyMaxCount = activityConfig.ShieldBuyMaxCount
		}
	}

	playerInfo := player.GetPlayerInfo()

	remainingCount := maxCount - playerInfo.ShieldCount
	if remainingCount < 0 {
		remainingCount = 0
	}

	remainingBuyCount := buyMaxCount - playerInfo.ShieldBuyCount
	if remainingBuyCount < 0 {
		remainingBuyCount = 0
	}

	return remainingCount, playerInfo.ShieldCount, remainingBuyCount, playerInfo.ShieldBuyCount
}

// 获取矿脉护盾状态
func (self *MineWarInfo) GetMineShieldStatus(mineId int64, groupId int) (bool, int64, int64, int64) {
	mine, ok := self.GetMineWarMine(groupId, mineId)
	if !ok {
		return false, 0, 0, 0
	}

	// 检查护盾是否激活
	isActive := self.IsShieldActive(mine)
	mineInfo := mine.GetMineInfo()

	var remainingTime int64 = 0
	if isActive {
		currentTime := time.Now().Unix()
		remainingTime = mineInfo.ShieldEndTime - currentTime
		if remainingTime < 0 {
			remainingTime = 0
		}
	}

	return isActive, mineInfo.ShieldStartTime, mineInfo.ShieldEndTime, remainingTime
}

// 获取公会所在的服务器ID
func (self *MineWarInfo) GetGuildServerId(guildId int64) int {
	// 遍历所有玩家数据，找到公会成员的服务器ID
	for _, group := range self.MineWarPlayer {
		for _, player := range group {
			// 这里需要根据实际的公会系统来获取公会ID
			// 暂时返回玩家的服务器ID作为示例
			if player.ServerId > 0 {
				return player.ServerId
			}
		}
	}
	return 0
}

// 获取玩家所在的服务器ID
func (self *MineWarInfo) GetPlayerServerId(uid int64) int {
	for _, group := range self.MineWarPlayer {
		if player, ok := group[uid]; ok {
			return player.ServerId
		}
	}
	return 0
}

// 获取分组ID
func (self *MineWarInfo) GetGroupId(serverId int, keyId int) int {
	// 参考citybroken的分组逻辑
	return GetMineWarMgr().GetGroupId(serverId, keyId)
}

// 处理队伍解锁相关请求
func (self *MineWarInfo) HandleMineTeamUnlock(req *RPC_MineTeamUnlockReq, res *RPC_MineTeamUnlockRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	player := self.GetOrCreateMineWarPlayer(req.Uid, req.GroupId)

	// 确保队伍解锁状态是最新的
	self.CheckTeamUnlock(player)

	// 获取配置
	unlockChallenge := TEAM_UNLOCK_CHALLENGE
	maxTeamCount := TEAM_MAX_UNLOCK_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			unlockChallenge = activityConfig.TeamUnlockChallenge
			maxTeamCount = activityConfig.TeamMaxUnlockCount
		}
	}

	playerInfo := player.GetPlayerInfo()

	switch req.Action {
	case 1: // 查询解锁状态
		res.RetCode = RETCODE_OK
		res.TotalChallengeCount = playerInfo.TotalChallengeCount
		res.UnlockedTeamCount = playerInfo.UnlockedTeamCount
		res.MaxTeamCount = maxTeamCount

		// 计算下次解锁需要的挑战次数
		if playerInfo.UnlockedTeamCount < maxTeamCount {
			nextUnlockTotal := unlockChallenge + (playerInfo.UnlockedTeamCount-1)*unlockChallenge
			if nextUnlockTotal <= playerInfo.TotalChallengeCount {
				// 已经满足解锁条件，但可能还没有触发解锁检查
				res.CanUnlock = true
				res.NextUnlockChallenge = 0
			} else {
				res.CanUnlock = false
				res.NextUnlockChallenge = nextUnlockTotal - playerInfo.TotalChallengeCount
			}
		} else {
			res.CanUnlock = false
			res.NextUnlockChallenge = 0
		}

	case 2: // 尝试解锁队伍
		// 这个动作实际上不需要特殊处理，因为解锁是自动的
		// 但可以用来强制检查解锁状态
		oldUnlockedCount := playerInfo.UnlockedTeamCount
		self.CheckTeamUnlock(player)

		if playerInfo.UnlockedTeamCount > oldUnlockedCount {
			res.RetCode = RETCODE_OK
			res.TotalChallengeCount = playerInfo.TotalChallengeCount
			res.UnlockedTeamCount = playerInfo.UnlockedTeamCount
			res.MaxTeamCount = maxTeamCount
			res.CanUnlock = (playerInfo.UnlockedTeamCount < maxTeamCount)
		} else {
			res.RetCode = RETCODE_DATA_ERROR // 没有新的队伍可以解锁
		}

	default:
		res.RetCode = RETCODE_DATA_ERROR
	}
}

// 处理挑战次数相关请求
func (self *MineWarInfo) HandleMineChallenge(req *RPC_MineChallengeReq, res *RPC_MineChallengeRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	switch req.Action {
	case 1: // 查询挑战次数
		remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerChallengeInfo(req.Uid, req.GroupId)

		// 获取购买消耗
		buyCost := 100 // 默认消耗
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				buyCost = activityConfig.ChallengeBuyCost
			}
		}

		res.RetCode = RETCODE_OK
		res.RemainingCount = remainingCount
		res.UsedCount = usedCount
		res.RemainingBuyCount = remainingBuyCount
		res.UsedBuyCount = usedBuyCount
		res.BuyCost = buyCost

	case 2: // 购买挑战次数
		success, cost := self.BuyChallengeCount(req.Uid, req.GroupId)
		if success {
			res.RetCode = RETCODE_OK
			res.BuyCost = cost

			// 返回更新后的挑战次数信息
			remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerChallengeInfo(req.Uid, req.GroupId)
			res.RemainingCount = remainingCount
			res.UsedCount = usedCount
			res.RemainingBuyCount = remainingBuyCount
			res.UsedBuyCount = usedBuyCount
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	default:
		res.RetCode = RETCODE_DATA_ERROR
	}
}

// 处理护盾相关请求
func (self *MineWarInfo) HandleMineShield(req *RPC_MineShieldReq, res *RPC_MineShieldRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	switch req.Action {
	case SHIELD_ACTION_ACTIVATE: // 激活护盾
		success := self.ActivateShield(req.Uid, req.GroupId, req.MineId)
		if success {
			res.RetCode = RETCODE_OK
			// 返回护盾状态
			isActive, startTime, endTime, remainingTime := self.GetMineShieldStatus(req.MineId, req.GroupId)
			res.ShieldActive = isActive
			res.ShieldStartTime = startTime
			res.ShieldEndTime = endTime
			res.RemainingTime = remainingTime

			// 返回玩家护盾次数信息
			remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerShieldInfo(req.Uid, req.GroupId)
			res.RemainingCount = remainingCount
			res.UsedCount = usedCount
			res.RemainingBuyCount = remainingBuyCount
			res.UsedBuyCount = usedBuyCount
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	case SHIELD_ACTION_DEACTIVATE: // 取消护盾
		success := self.DeactivateShield(req.Uid, req.GroupId, req.MineId)
		if success {
			res.RetCode = RETCODE_OK
			res.ShieldActive = false
			res.ShieldStartTime = 0
			res.ShieldEndTime = 0
			res.RemainingTime = 0
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	case SHIELD_ACTION_QUERY: // 查询护盾状态
		// 获取矿脉护盾状态
		isActive, startTime, endTime, remainingTime := self.GetMineShieldStatus(req.MineId, req.GroupId)
		res.ShieldActive = isActive
		res.ShieldStartTime = startTime
		res.ShieldEndTime = endTime
		res.RemainingTime = remainingTime

		// 获取玩家护盾次数信息
		remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerShieldInfo(req.Uid, req.GroupId)
		res.RemainingCount = remainingCount
		res.UsedCount = usedCount
		res.RemainingBuyCount = remainingBuyCount
		res.UsedBuyCount = usedBuyCount

		// 获取购买消耗
		buyCost := 200 // 默认消耗
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				buyCost = activityConfig.ShieldBuyCost
			}
		}
		res.BuyCost = buyCost
		res.RetCode = RETCODE_OK

	case 4: // 购买护盾次数
		success, cost := self.BuyShieldCount(req.Uid, req.GroupId)
		if success {
			res.RetCode = RETCODE_OK
			res.BuyCost = cost

			// 返回更新后的护盾次数信息
			remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerShieldInfo(req.Uid, req.GroupId)
			res.RemainingCount = remainingCount
			res.UsedCount = usedCount
			res.RemainingBuyCount = remainingBuyCount
			res.UsedBuyCount = usedBuyCount
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	default:
		res.RetCode = RETCODE_DATA_ERROR
	}
}

func (self *MineWarInfo) Update() {
	// TODO: 实现更新逻辑
}

func (self *MineWarInfo) OnTimer() {
	// TODO: 实现定时器逻辑
	self.CheckMineUnlock()
	self.UpdateDailyBonus()
	self.CheckWeeklySettlement()
	self.CheckDailyReset()
	self.CheckShieldExpiration()

	// 检查是否需要发送每日产出奖励
	self.CheckDailyOutputReward()

	// 每分钟更新一次积分
	self.UpdatePlayerScores()
}

// 检查每日产出奖励发送
func (self *MineWarInfo) CheckDailyOutputReward() {
	now := time.Now()

	// 每天特定时间发送产出奖励（例如每天21:00）
	if now.Hour() == 21 && now.Minute() == 0 {
		self.SendDailyOutputRewards()
	}
}

// 每分钟更新玩家积分
func (self *MineWarInfo) UpdatePlayerScores() {
	// 检查是否在活动期间
	if !GetMineWarMgr().IsActivityPeriod() {
		return
	}

	// 检查是否在战斗时间
	if !GetMineWarMgr().IsWarTime() {
		return
	}

	now := time.Now()

	// 遍历所有矿脉，为占领者增加积分
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil && mine.fightInfo.Uid > 0 {
				// 计算积分
				score := self.CalculateMineScore(mine, now)
				if score > 0 {
					// 更新玩家积分
					self.UpdatePlayerScore(mine.fightInfo.Uid, mine.fightInfo.Server, score, mine.fightInfo)

					// 记录积分更新日志
					utils.LogDebug("矿脉积分更新: 玩家[%d] 矿脉[%d] 积分[%d]",
						mine.fightInfo.Uid, mine.MineId, score)
				}
			}
		}
	}
}

// 计算矿脉积分
func (self *MineWarInfo) CalculateMineScore(mine *MineWarMine, currentTime time.Time) int64 {
	if mine == nil || mine.fightInfo == nil {
		return 0
	}

	mineInfo := mine.GetMineInfo()

	// 获取矿脉配置
	mineConfig := GetMineWarMgr().GetMineWarOutputConfig(mineInfo.Level)
	if mineConfig == nil {
		return 0
	}

	// 基础积分（每分钟）
	baseScore := int64(mineConfig.OutputNum3) // 使用配置表中的积分产出
	if baseScore <= 0 {
		// 如果配置表没有设置积分，使用默认值
		baseScore = int64(mineInfo.Level * 10) // 矿脉等级 * 10
	}

	// 护盾期间积分加成
	if self.IsShieldActive(mine) {
		baseScore = baseScore * 120 / 100 // 护盾期间积分提升20%
	}

	// 5级矿脉随机加成
	if mineInfo.Level == 5 && mineInfo.BonusActive {
		bonusRate := 150 // 默认50%加成
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				bonusRate = 100 + activityConfig.BonusRate
			}
		}
		baseScore = baseScore * int64(bonusRate) / 100
	}

	// 夜间双倍积分（21:00-22:00）
	if currentTime.Hour() >= 21 && currentTime.Hour() < 22 {
		baseScore = baseScore * 2
	}

	return baseScore
}

// 获取玩家当前积分
func (self *MineWarInfo) GetPlayerScore(uid int64, serverId int) int64 {
	groupId := self.GetGroupId(serverId, int(self.MineWarConfig.KeyId))
	rankInfo := GetRankMgr().GetMyRank(RANK_TYPE_MINE_WAR_PLAYER, groupId, uid)
	if rankInfo != nil {
		return rankInfo.Num
	}
	return 0
}

// 获取公会当前积分
func (self *MineWarInfo) GetGuildScore(guildId int64, serverId int) int64 {
	groupId := self.GetGroupId(serverId, int(self.MineWarConfig.KeyId))
	rankInfo := GetRankMgr().GetMyRank(RANK_TYPE_MINE_WAR_GUILD, groupId, guildId)
	if rankInfo != nil {
		return rankInfo.Num
	}
	return 0
}

// 获取服务器当前积分
func (self *MineWarInfo) GetServerScore(serverId int) int64 {
	groupId := self.GetGroupId(serverId, int(self.MineWarConfig.KeyId))
	rankInfo := GetRankMgr().GetMyRank(RANK_TYPE_MINE_WAR_SERVER, groupId, int64(serverId))
	if rankInfo != nil {
		return rankInfo.Num
	}
	return 0
}

// 处理积分查询相关请求
func (self *MineWarInfo) HandleMineScore(req *RPC_MineScoreReq, res *RPC_MineScoreRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	serverId := 0
	// 从玩家数据中获取服务器ID
	if req.Uid > 0 {
		serverId = self.GetPlayerServerId(req.Uid)
	}

	if serverId <= 0 {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	switch req.Action {
	case 1: // 查询个人积分
		res.PlayerScore = self.GetPlayerScore(req.Uid, serverId)
		res.RetCode = RETCODE_OK

	case 2: // 查询公会积分
		if req.GuildId > 0 {
			res.GuildScore = self.GetGuildScore(req.GuildId, serverId)
		}
		res.RetCode = RETCODE_OK

	case 3: // 查询服务器积分
		res.ServerScore = self.GetServerScore(serverId)
		res.RetCode = RETCODE_OK

	default:
		// 查询所有积分
		res.PlayerScore = self.GetPlayerScore(req.Uid, serverId)
		if req.GuildId > 0 {
			res.GuildScore = self.GetGuildScore(req.GuildId, serverId)
		}
		res.ServerScore = self.GetServerScore(serverId)
		res.RetCode = RETCODE_OK
	}
}

// 检查护盾过期
func (self *MineWarInfo) CheckShieldExpiration() {
	currentTime := time.Now().Unix()

	// 检查所有矿脉的护盾状态
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			mineInfo := mine.GetMineInfo()
			if mineInfo.ShieldActive && currentTime > mineInfo.ShieldEndTime {
				// 护盾已过期，自动取消
				mineInfo.ShieldActive = false
				mineInfo.ShieldStartTime = 0
				mineInfo.ShieldEndTime = 0
				mineInfo.ShieldDuration = 0
			}
		}
	}
}

// 检查每日重置(0点重置挑战次数)
func (self *MineWarInfo) CheckDailyReset() {
	t := time.Now()

	// 每天0点的第一分钟执行重置
	if t.Hour() != 0 || t.Minute() != 0 {
		return
	}

	today := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
	todayUnix := today.Unix()

	// 检查是否已经重置过了（避免重复重置）
	if self.MineWarConfig != nil {
		configInfo := self.MineWarConfig.configInfo
		if configInfo != nil && configInfo.LastDailyResetTime >= todayUnix {
			return // 今天已经重置过了
		}
		if configInfo != nil {
			configInfo.LastDailyResetTime = todayUnix
		}
	}

	// 重置所有玩家的挑战次数
	for _, group := range self.MineWarPlayer {
		for _, player := range group {
			self.CheckAndResetDailyChallengeCount(player)
		}
	}
}

// 检查周结算
func (self *MineWarInfo) CheckWeeklySettlement() {
	t := time.Now()

	// 检查是否为周日20:00的第一分钟
	if t.Weekday() != time.Sunday || t.Hour() != SETTLEMENT_HOUR || t.Minute() != 0 {
		return
	}

	// 检查是否已经结算过了（避免重复结算）
	if self.MineWarConfig != nil {
		currentWeek := self.GetCurrentWeek()
		configInfo := self.MineWarConfig.configInfo
		if configInfo != nil && configInfo.LastSettlementWeek >= currentWeek {
			return // 本周已经结算过了
		}
		if configInfo != nil {
			configInfo.LastSettlementWeek = currentWeek
		}
	}

	// 执行结算
	self.DoWeeklySettlement()
}

// 获取当前周数（从活动开始计算）
func (self *MineWarInfo) GetCurrentWeek() int64 {
	if self.MineWarConfig == nil {
		return 0
	}

	now := time.Now().Unix()
	if now < self.MineWarConfig.StartTime {
		return 0
	}

	// 计算从活动开始到现在的周数
	weeksSinceStart := (now - self.MineWarConfig.StartTime) / (7 * utils.DAY_SECS)
	return weeksSinceStart + 1 // 第一周为1
}

// 设置区域服务器分配
func (self *MineWarInfo) SetRegionServerAllocation(region int, serverIds []int) {
	// 更新该区域内所有1-3级矿脉的服务器分配
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			mineInfo := mine.GetMineInfo()
			if mineInfo.Region == region && mineInfo.Level <= MINE_LEVEL_PRIVATE_MAX {
				mine.SetServerIds(serverIds)
			}
		}
	}
}

// 获取区域内的服务器列表
func (self *MineWarInfo) GetRegionServers(region int) []int {
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			mineInfo := mine.GetMineInfo()
			if mineInfo.Region == region && mineInfo.Level <= MINE_LEVEL_PRIVATE_MAX {
				return mine.GetServerIds()
			}
		}
	}
	return []int{}
}

// 检查服务器是否属于指定区域
func (self *MineWarInfo) IsServerInRegion(serverId int, region int) bool {
	regionServers := self.GetRegionServers(region)
	for _, id := range regionServers {
		if id == serverId {
			return true
		}
	}
	return false
}

// 初始化区域服务器分配的示例方法
func (self *MineWarInfo) InitializeRegionServerAllocation() {
	// 示例：将服务器分配到不同区域
	// 区域1：服务器1,2,3
	self.SetRegionServerAllocation(1, []int{1, 2, 3})

	// 区域2：服务器4,5,6
	self.SetRegionServerAllocation(2, []int{4, 5, 6})

	// 区域3：服务器7,8,9
	self.SetRegionServerAllocation(3, []int{7, 8, 9})

	// 区域4：服务器10,11,12
	self.SetRegionServerAllocation(4, []int{10, 11, 12})
}

// 执行周结算
func (self *MineWarInfo) DoWeeklySettlement() {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 计算最终排名
	guildRanking := self.CalculateGuildRanking()
	playerRanking := self.CalculatePlayerRanking()
	serverRanking := self.CalculateServerRanking()

	// 发放奖励
	self.DistributeGuildRewards(guildRanking)
	self.DistributePlayerRewards(playerRanking)
	self.DistributeServerRewards(serverRanking)

	// 清理数据，准备下一轮
	self.PrepareNextSeason()
}

// 计算公会排名
func (self *MineWarInfo) CalculateGuildRanking() []GuildRankInfo {
	ranking := make([]GuildRankInfo, 0)

	if self.MineWarConfig == nil {
		return ranking
	}

	groupId := self.GetGroupId(0, int(self.MineWarConfig.KeyId))

	// 从排行榜系统获取公会排名
	req := &RPC_GetRankReq{
		RankId:   RANK_TYPE_MINE_WAR_GUILD,
		Period:   groupId,
		ServerId: 0,
	}
	res := &RPC_GetRankRes{}
	GetRankMgr().GetRank(req, res)

	// 转换为GuildRankInfo格式
	for i, rankInfo := range res.RankInfo {
		if rankInfo != nil {
			ranking = append(ranking, GuildRankInfo{
				GuildId: rankInfo.Uid,
				Score:   rankInfo.Num,
				Rank:    i + 1,
			})
		}
	}

	return ranking
}

// 计算个人排名
func (self *MineWarInfo) CalculatePlayerRanking() []PlayerRankInfo {
	ranking := make([]PlayerRankInfo, 0)

	if self.MineWarConfig == nil {
		return ranking
	}

	groupId := self.GetGroupId(0, int(self.MineWarConfig.KeyId))

	// 从排行榜系统获取个人排名
	req := &RPC_GetRankReq{
		RankId:   RANK_TYPE_MINE_WAR_PLAYER,
		Period:   groupId,
		ServerId: 0,
	}
	res := &RPC_GetRankRes{}
	GetRankMgr().GetRank(req, res)

	// 转换为PlayerRankInfo格式
	for i, rankInfo := range res.RankInfo {
		if rankInfo != nil {
			ranking = append(ranking, PlayerRankInfo{
				Uid:   rankInfo.Uid,
				Score: rankInfo.Num,
				Rank:  i + 1,
			})
		}
	}

	return ranking
}

// 计算服务器排名
func (self *MineWarInfo) CalculateServerRanking() []ServerRankInfo {
	ranking := make([]ServerRankInfo, 0)

	if self.MineWarConfig == nil {
		return ranking
	}

	groupId := self.GetGroupId(0, int(self.MineWarConfig.KeyId))

	// 从排行榜系统获取服务器排名
	req := &RPC_GetRankReq{
		RankId:   RANK_TYPE_MINE_WAR_SERVER,
		Period:   groupId,
		ServerId: 0,
	}
	res := &RPC_GetRankRes{}
	GetRankMgr().GetRank(req, res)

	// 转换为ServerRankInfo格式
	for i, rankInfo := range res.RankInfo {
		if rankInfo != nil {
			ranking = append(ranking, ServerRankInfo{
				ServerId: int(rankInfo.Uid),
				Score:    rankInfo.Num,
				Rank:     i + 1,
			})
		}
	}

	return ranking
}

// 发放公会奖励
func (self *MineWarInfo) DistributeGuildRewards(ranking []GuildRankInfo) {
	if len(ranking) == 0 {
		return
	}

	// 按服务器分组发送奖励
	serverRewards := make(map[int][]GuildRankInfo)
	for _, guildRank := range ranking {
		// 获取公会所在的服务器ID
		serverId := self.GetGuildServerId(guildRank.GuildId)
		if serverId > 0 {
			if _, ok := serverRewards[serverId]; !ok {
				serverRewards[serverId] = make([]GuildRankInfo, 0)
			}
			serverRewards[serverId] = append(serverRewards[serverId], guildRank)
		}
	}

	// 向各服务器发送公会奖励事件
	for serverId, rewards := range serverRewards {
		msgData := utils.HF_JtoA(rewards)
		core.GetCenterApp().AddEvent(serverId, MINE_WAR_EVENT_GUILD_REWARD, 0,
			0, int(self.MineWarConfig.KeyId), msgData)
	}
}

// 发放个人奖励
func (self *MineWarInfo) DistributePlayerRewards(ranking []PlayerRankInfo) {
	if len(ranking) == 0 {
		return
	}

	// 按服务器分组发送奖励
	serverRewards := make(map[int][]PlayerRankInfo)
	for _, playerRank := range ranking {
		// 获取玩家所在的服务器ID
		serverId := self.GetPlayerServerId(playerRank.Uid)
		if serverId > 0 {
			if _, ok := serverRewards[serverId]; !ok {
				serverRewards[serverId] = make([]PlayerRankInfo, 0)
			}
			serverRewards[serverId] = append(serverRewards[serverId], playerRank)
		}
	}

	// 向各服务器发送个人奖励事件
	for serverId, rewards := range serverRewards {
		msgData := utils.HF_JtoA(rewards)
		core.GetCenterApp().AddEvent(serverId, MINE_WAR_EVENT_PLAYER_REWARD, 0,
			0, int(self.MineWarConfig.KeyId), msgData)
	}
}

// 发放服务器奖励
func (self *MineWarInfo) DistributeServerRewards(ranking []ServerRankInfo) {
	if len(ranking) == 0 {
		return
	}

	// 直接向各服务器发送奖励事件
	for _, serverRank := range ranking {
		msgData := utils.HF_JtoA(serverRank)
		core.GetCenterApp().AddEvent(serverRank.ServerId, MINE_WAR_EVENT_SERVER_REWARD, 0,
			0, int(self.MineWarConfig.KeyId), msgData)
	}
}

// 发送矿脉占领通知
func (self *MineWarInfo) SendMineOccupyNotify(mine *MineWarMine, newOwnerId int64, oldOwnerId int64) {
	if mine == nil {
		return
	}

	// 构造通知数据
	notifyData := struct {
		MineId     int64 `json:"mine_id"`
		MineLevel  int   `json:"mine_level"`
		NewOwnerId int64 `json:"new_owner_id"`
		OldOwnerId int64 `json:"old_owner_id"`
		OccupyTime int64 `json:"occupy_time"`
		ActivityId int   `json:"activity_id"`
	}{
		MineId:     mine.MineId,
		MineLevel:  mine.GetMineInfo().Level,
		NewOwnerId: newOwnerId,
		OldOwnerId: oldOwnerId,
		OccupyTime: time.Now().Unix(),
		ActivityId: int(self.MineWarConfig.KeyId),
	}

	msgData := utils.HF_JtoA(notifyData)

	// 通知新占领者
	if newOwnerId > 0 {
		newOwnerServerId := self.GetPlayerServerId(newOwnerId)
		if newOwnerServerId > 0 {
			core.GetCenterApp().AddEvent(newOwnerServerId, MINE_WAR_EVENT_OCCUPY_NOTIFY, newOwnerId,
				mine.MineId, 1, msgData) // param1=1 表示占领成功
		}
	}

	// 通知被夺取者
	if oldOwnerId > 0 && oldOwnerId != newOwnerId {
		oldOwnerServerId := self.GetPlayerServerId(oldOwnerId)
		if oldOwnerServerId > 0 {
			core.GetCenterApp().AddEvent(oldOwnerServerId, MINE_WAR_EVENT_OCCUPY_NOTIFY, oldOwnerId,
				mine.MineId, 0, msgData) // param1=0 表示被夺取
		}
	}
}

// 发送矿脉攻击通知
func (self *MineWarInfo) SendMineAttackNotify(mine *MineWarMine, attackerId int64, defenderId int64, result int) {
	if mine == nil {
		return
	}

	// 构造通知数据
	notifyData := struct {
		MineId     int64 `json:"mine_id"`
		MineLevel  int   `json:"mine_level"`
		AttackerId int64 `json:"attacker_id"`
		DefenderId int64 `json:"defender_id"`
		Result     int   `json:"result"` // 1:攻击成功 0:攻击失败
		AttackTime int64 `json:"attack_time"`
		ActivityId int   `json:"activity_id"`
	}{
		MineId:     mine.MineId,
		MineLevel:  mine.Level,
		AttackerId: attackerId,
		DefenderId: defenderId,
		Result:     result,
		AttackTime: time.Now().Unix(),
		ActivityId: int(self.MineWarConfig.KeyId),
	}

	msgData := utils.HF_JtoA(notifyData)

	// 通知攻击者
	if attackerId > 0 {
		attackerServerId := self.GetPlayerServerId(attackerId)
		if attackerServerId > 0 {
			core.GetCenterApp().AddEvent(attackerServerId, MINE_WAR_EVENT_ATTACK_NOTIFY, attackerId,
				mine.MineId, result, msgData)
		}
	}

	// 通知防守者
	if defenderId > 0 && defenderId != attackerId {
		defenderServerId := self.GetPlayerServerId(defenderId)
		if defenderServerId > 0 {
			core.GetCenterApp().AddEvent(defenderServerId, MINE_WAR_EVENT_ATTACK_NOTIFY, defenderId,
				mine.MineId, 1-result, msgData) // 防守者的结果与攻击者相反
		}
	}
}

// 发送矿脉产出奖励
func (self *MineWarInfo) SendMineOutputReward(uid int64, mineId int64, outputItems []MineOutputItem) {
	if uid <= 0 || len(outputItems) == 0 {
		return
	}

	serverId := self.GetPlayerServerId(uid)
	if serverId <= 0 {
		return
	}

	// 构造奖励数据
	rewardData := struct {
		Uid         int64            `json:"uid"`
		MineId      int64            `json:"mine_id"`
		OutputItems []MineOutputItem `json:"output_items"`
		RewardTime  int64            `json:"reward_time"`
		ActivityId  int              `json:"activity_id"`
	}{
		Uid:         uid,
		MineId:      mineId,
		OutputItems: outputItems,
		RewardTime:  time.Now().Unix(),
		ActivityId:  int(self.MineWarConfig.KeyId),
	}

	msgData := utils.HF_JtoA(rewardData)
	core.GetCenterApp().AddEvent(serverId, MINE_WAR_EVENT_PLAYER_REWARD, uid,
		mineId, 0, msgData)
}

// 批量发送每日产出奖励
func (self *MineWarInfo) SendDailyOutputRewards() {
	// 遍历所有矿脉，为占领者发送产出奖励
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil && mine.fightInfo.Uid > 0 {
				// 计算产出
				outputItems := self.CalculateMineOutput(mine)
				if len(outputItems) > 0 {
					self.SendMineOutputReward(mine.fightInfo.Uid, mine.MineId, outputItems)
				}
			}
		}
	}
}

// 计算矿脉产出
func (self *MineWarInfo) CalculateMineOutput(mine *MineWarMine) []MineOutputItem {
	if mine == nil || mine.fightInfo == nil || mine.fightInfo.Uid <= 0 {
		return nil
	}

	// 计算产出时间
	currentTime := time.Now().Unix()
	outputTime := currentTime - mine.StartTime

	// 限制最大累积时间
	if outputTime > mine.MaxAccumTime {
		outputTime = mine.MaxAccumTime
	}

	// 获取基础产出率
	baseRate := self.GetMineBaseOutputRate(mine.Level)
	actualRate := self.GetOutputRate(baseRate)

	// 如果有加成，增加产出
	if mine.BonusActive {
		bonusRate := 150 // 默认50%加成
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				bonusRate = 100 + activityConfig.BonusRate
			}
		}
		actualRate = actualRate * int64(bonusRate) / 100
	}

	// 护盾期间稳定收益：护盾激活期间产出效率提升
	if self.IsShieldActive(mine) {
		// 护盾期间产出效率提升20%
		actualRate = actualRate * 120 / 100
	}

	totalOutput := outputTime * actualRate

	// 构造产出物品
	outputItems := make([]MineOutputItem, 0)
	if totalOutput > 0 {
		// 从配置表获取产出物品类型
		outputConfig := GetMineWarMgr().GetMineWarOutputConfig(mine.Level)
		if outputConfig != nil {
			// 添加第一种产出物品
			if outputConfig.OutputItem1 > 0 {
				outputItems = append(outputItems, MineOutputItem{
					ItemId:  outputConfig.OutputItem1,
					ItemNum: int(totalOutput * int64(outputConfig.OutputNum1) / 100),
				})
			}
			// 添加第二种产出物品
			if outputConfig.OutputItem2 > 0 {
				outputItems = append(outputItems, MineOutputItem{
					ItemId:  outputConfig.OutputItem2,
					ItemNum: int(totalOutput * int64(outputConfig.OutputNum2) / 100),
				})
			}
		} else {
			// 默认产出
			outputItems = append(outputItems, MineOutputItem{
				ItemId:  1001, // 默认货币ID
				ItemNum: int(totalOutput),
			})
		}
	}

	return outputItems
}

// 准备下一赛季
func (self *MineWarInfo) PrepareNextSeason() {
	// 清理矿脉占领状态
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil {
				mine.fightInfo = nil
			}
			mine.StartTime = 0
			mine.ShieldActive = false
			mine.ShieldStartTime = 0
			mine.ShieldEndTime = 0
			mine.ShieldDuration = 0
			mine.BonusActive = false
		}
	}

	// 清理玩家数据（保留累计挑战次数和解锁状态）
	for _, group := range self.MineWarPlayer {
		for _, player := range group {
			player.TeamMine = make(map[int64]int64)
			player.ChallengeCount = 0
			player.ChallengeBuyCount = 0
			player.ShieldCount = 0
			player.ShieldBuyCount = 0
			// 注意：不重置 TotalChallengeCount 和 UnlockedTeamCount
		}
	}

	// 重置配置状态
	if self.MineWarConfig != nil {
		self.MineWarConfig.LastSettlementWeek = 0
		self.MineWarConfig.LastDailyResetTime = 0
	}
}

// 排名信息结构体
type GuildRankInfo struct {
	GuildId int64 `json:"guild_id"`
	Score   int64 `json:"score"`
	Rank    int   `json:"rank"`
}

type PlayerRankInfo struct {
	Uid   int64 `json:"uid"`
	Score int64 `json:"score"`
	Rank  int   `json:"rank"`
}

type ServerRankInfo struct {
	ServerId int   `json:"server_id"`
	Score    int64 `json:"score"`
	Rank     int   `json:"rank"`
}

// 检查矿脉解锁
func (self *MineWarInfo) CheckMineUnlock() {
	if !GetMineWarMgr().IsActivityPeriod() {
		return
	}

	// 计算活动开始后的天数
	activityDay := self.GetActivityDay()

	// 检查所有矿脉的解锁状态
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if !mine.IsUnlocked && activityDay >= mine.UnlockDay {
				mine.IsUnlocked = true
				// TODO: 发送解锁通知
			}
		}
	}
}

// 更新每日加成(5级矿脉随机获得产出加成)
func (self *MineWarInfo) UpdateDailyBonus() {
	t := time.Now()
	// 每天0点更新加成
	if t.Hour() != 0 || t.Minute() != 0 {
		return
	}

	// 重置所有5级矿脉的加成状态
	level5Mines := make([]*MineWarMine, 0)
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.Level == 5 && mine.IsUnlocked {
				mine.BonusActive = false
				level5Mines = append(level5Mines, mine)
			}
		}
	}

	// 随机选择一个5级矿脉获得加成
	if len(level5Mines) > 0 {
		randomIndex := utils.HF_RandInt(0, len(level5Mines)-1)
		level5Mines[randomIndex].BonusActive = true
		// TODO: 发送加成通知
	}
}

// 获取活动开始后的天数
func (self *MineWarInfo) GetActivityDay() int {
	if self.MineWarConfig == nil {
		return 0
	}

	now := time.Now().Unix()
	startTime := self.MineWarConfig.StartTime
	daysPassed := (now - startTime) / utils.DAY_SECS

	return int(daysPassed) + 1 // 第一天为第1天
}

// 检查矿脉是否可以攻击
func (self *MineWarInfo) CanAttackMine(attackerServerId int, targetMine *MineWarMine) bool {
	// 检查是否在战斗时间
	if !GetMineWarMgr().IsWarTime() {
		return false
	}

	// 检查矿脉是否已解锁
	if !targetMine.IsUnlocked {
		return false
	}

	// 1-3级矿脉只能分配到该区域的服务器攻击
	if targetMine.Level <= MINE_LEVEL_PRIVATE_MAX {
		return targetMine.HasServerId(attackerServerId)
	}

	// 4-7级矿脉为公共矿脉，所有服务器都可以攻击
	return true
}

// 计算产出效率(休赛期降低效率)
func (self *MineWarInfo) GetOutputRate(baseRate int64) int64 {
	if GetMineWarMgr().IsRestPeriod() {
		// 从配置表获取休赛期产出效率
		restRate := REST_PERIOD_OUTPUT_RATE // 默认50%
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				restRate = activityConfig.RestOutputRate
			}
		}
		return baseRate * int64(restRate) / 100
	}
	return baseRate
}

// ! 矿战管理类
var s_mineWarMgr *MineWarMgr = nil

func GetMineWarMgr() *MineWarMgr {
	if s_mineWarMgr == nil {
		s_mineWarMgr = new(MineWarMgr)
		s_mineWarMgr.LoadCsv()
	}
	return s_mineWarMgr
}

func (self *MineWarMgr) LoadCsv() {
	self.MineWarConfigMap = make(map[int64]*MineWarConfig)

	// 加载矿脉等级配置
	self.MineWarLevelConfigMap = make(map[int]*MineWarLevelConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Level_Config", &self.MineWarLevelConfigMap)

	// 加载矿脉活动配置
	self.MineWarActivityConfigMap = make(map[int]*MineWarActivityConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Activity_Config", &self.MineWarActivityConfigMap)

	// 加载矿脉产出配置
	self.MineWarOutputConfigMap = make(map[int]*MineWarOutputConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Output_Config", &self.MineWarOutputConfigMap)

	return
}

// 获取矿脉等级配置
func (self *MineWarMgr) GetMineWarLevelConfig(level int) *MineWarLevelConfig {
	if config, ok := self.MineWarLevelConfigMap[level]; ok {
		return config
	}
	return nil
}

// 获取矿脉活动配置
func (self *MineWarMgr) GetMineWarActivityConfig(group int) *MineWarActivityConfig {
	if config, ok := self.MineWarActivityConfigMap[group]; ok {
		return config
	}
	return nil
}

// 获取矿脉产出配置
func (self *MineWarMgr) GetMineWarOutputConfig(level int) *MineWarOutputConfig {
	if config, ok := self.MineWarOutputConfigMap[level]; ok {
		return config
	}
	return nil
}

func (self *MineWarMgr) CheckMineWar(req *RPC_UpdateActivityReq) {
	if self.MineWarInfo.MineWarConfig.EndTime == 0 {
		self.MineWarInfo.InitMineWarConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.GetAllData()
	} else if self.MineWarInfo.MineWarConfig.EndTime != req.EndTime {
		self.MineWarInfo.InitMineWarConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.MineWarInfo.CleanMineWar()
	}
}

func (self *MineWarInfo) InitMineWarConfig(starttime, rewardtime, enttime int64, keyid int) {
	self.lock.Lock()
	defer self.lock.Unlock()
	self.MineWarConfig.KeyId = keyid
	self.MineWarConfig.StartTime = starttime
	self.MineWarConfig.EndTime = enttime

	// 从配置表获取活动配置
	activityConfig := GetMineWarMgr().GetMineWarActivityConfig(keyid / 1000)
	if activityConfig != nil {
		// 使用配置表中的时间设置
		self.MineWarConfig.PrepareTime = self.MineWarConfig.StartTime + 1*utils.DAY_SECS
		self.MineWarConfig.BattleTime = self.MineWarConfig.PrepareTime + int64(activityConfig.KeepTime)*utils.DAY_SECS
		self.MineWarConfig.ResultTime = self.MineWarConfig.BattleTime + 1*utils.DAY_SECS

		// 设置其他配置
		self.MineWarConfig.KeepTime = int64(activityConfig.KeepTime)
		self.MineWarConfig.AttackNum = activityConfig.TeamMaxNum
	} else {
		// 如果没有配置，使用默认值
		self.MineWarConfig.PrepareTime = self.MineWarConfig.StartTime + 1*utils.DAY_SECS
		self.MineWarConfig.BattleTime = self.MineWarConfig.PrepareTime + 5*utils.DAY_SECS
		self.MineWarConfig.ResultTime = self.MineWarConfig.BattleTime + 1*utils.DAY_SECS
	}
}

func (self *MineWarInfo) CleanMineWar() {
	self.lock.Lock()
	defer self.lock.Unlock()
	for _, group := range self.MineWarMines {
		for _, v := range group {
			if v.SeasonTime != self.MineWarConfig.EndTime {
				v.SeasonTime = self.MineWarConfig.EndTime
				v.fightInfo = NewMineWarFightInfo(v.MineId)
				v.StartTime = 0
				v.ShieldTime = 0
				v.AttackUid = 0
				v.AttackTime = 0

				// 重新初始化矿脉属性
				v.InitMineProperties(v.MineId)
				v.Decode()
			}
		}
	}
	self.MineWarPlayer = make(map[int]map[int64]*MineWarPlayer)
	self.MineWarServer = make(map[int]map[int]*MineWarServer)
	self.MineWarServerGroup = make(map[int]int)
}

// 初始化服务器矿脉分配
func (self *MineWarInfo) InitServerMineAllocation(groupId int, serverIds []int) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 确保组存在
	if _, ok := self.MineWarMines[groupId]; !ok {
		self.MineWarMines[groupId] = make(map[int64]*MineWarMine)
	}

	// 为每个服务器分配1-3级矿脉到四个角落
	for i, serverId := range serverIds {
		region := (i % 4) + 1 // 分配到4个区域

		// 为每个服务器在指定区域创建1-3级矿脉
		for level := 1; level <= MINE_LEVEL_PRIVATE_MAX; level++ {
			// 生成矿脉ID: 服务器ID * 1000 + 区域 * 100 + 等级
			mineId := int64(serverId*1000 + region*100 + level)

			mine := NewMineWarUser(mineId, groupId)
			mine.SetServerIds([]int{serverId}) // 设置单个服务器ID
			mine.Region = region
			mine.Level = level
			mine.IsPublic = false
			mine.IsUnlocked = true // 1-3级矿脉默认解锁

			self.MineWarMines[groupId][mineId] = mine
		}
	}

	// 创建公共矿脉(4-7级)
	self.InitPublicMines(groupId)
}

// 初始化公共矿脉
func (self *MineWarInfo) InitPublicMines(groupId int) {
	// 创建4-7级公共矿脉，分布在地图中央区域
	for level := MINE_LEVEL_PUBLIC_MIN; level <= MINE_LEVEL_PUBLIC_MAX; level++ {
		// 从配置表获取矿脉数量
		mineCount := level - 2 // 默认值：4级2个，5级3个，6级4个，7级5个
		config := GetMineWarMgr().GetMineWarLevelConfig(level)
		if config != nil && config.MineCount > 0 {
			mineCount = config.MineCount
		}

		for i := 1; i <= mineCount; i++ {
			// 生成公共矿脉ID: 9000000 + 等级 * 1000 + 序号
			mineId := int64(9000000 + level*1000 + i)

			mine := NewMineWarUser(mineId, groupId)
			mine.SetServerIds([]int{}) // 公共矿脉不属于特定服务器
			mine.Region = 0            // 公共区域
			mine.Level = level
			mine.IsPublic = true
			mine.IsUnlocked = (level == 4) // 4级矿脉默认解锁，其他需要等待

			self.MineWarMines[groupId][mineId] = mine
		}
	}
}

// 根据配置表初始化所有矿脉
func (self *MineWarInfo) InitMinesFromConfig(groupId int, serverIds []int) {
	// 确保组存在
	if _, ok := self.MineWarMines[groupId]; !ok {
		self.MineWarMines[groupId] = make(map[int64]*MineWarMine)
	}

	// 初始化所有等级的矿脉
	for level := 1; level <= MINE_LEVEL_MAX; level++ {
		config := GetMineWarMgr().GetMineWarLevelConfig(level)
		if config == nil {
			continue
		}

		if config.IsPublic == 0 {
			// 私有矿脉，为每个服务器创建
			for i, serverId := range serverIds {
				region := (i % 4) + 1 // 分配到4个区域

				for j := 1; j <= config.MineCount; j++ {
					// 生成矿脉ID: 服务器ID * 10000 + 等级 * 1000 + 序号
					mineId := int64(serverId*10000 + level*1000 + j)

					mine := NewMineWarUser(mineId, groupId)
					mine.SetServerIds([]int{serverId}) // 设置单个服务器ID
					mine.Region = region
					mine.Level = level
					mine.IsPublic = false
					mine.IsUnlocked = (level <= MINE_LEVEL_PRIVATE_MAX) // 1-3级默认解锁

					self.MineWarMines[groupId][mineId] = mine
				}
			}
		} else {
			// 公共矿脉
			for i := 1; i <= config.MineCount; i++ {
				// 生成公共矿脉ID: 9000000 + 等级 * 1000 + 序号
				mineId := int64(9000000 + level*1000 + i)

				mine := NewMineWarUser(mineId, groupId)
				mine.SetServerIds([]int{}) // 公共矿脉不属于特定服务器
				mine.Region = 0            // 公共区域
				mine.Level = level
				mine.IsPublic = true
				mine.IsUnlocked = (level == 4) // 4级矿脉默认解锁，其他需要等待

				self.MineWarMines[groupId][mineId] = mine
			}
		}
	}
}

func NewMineWarFightInfo(id int64) *model.JS_FightInfo {
	data := new(model.JS_FightInfo)
	data.Rankid = int(id)
	return data
}

func NewMineWarUser(mineId int64, groupid int) *MineWarMine {
	data := new(MineWarMine)
	data.MineId = mineId
	data.GroupId = groupid
	data.SeasonTime = GetMineWarMgr().MineWarInfo.MineWarConfig.EndTime
	data.fightInfo = NewMineWarFightInfo(mineId)
	data.StartTime = 0

	// 根据矿点ID初始化矿脉属性
	data.InitMineProperties(mineId)

	db.InsertTable("tbl_minewaruser", data, 0, false)
	data.Init("tbl_minewaruser", data, false)
	return data
}

// 初始化矿脉属性
func (self *MineWarMine) InitMineProperties(mineId int64) {
	// 根据矿点ID确定等级和属性
	// 这里简化处理，实际应该从配置表读取
	level := int((mineId % 7) + 1) // 1-7级
	self.Level = level

	// 从配置表获取矿脉等级配置
	config := GetMineWarMgr().GetMineWarLevelConfig(level)
	if config != nil {
		self.UnlockDay = config.UnlockDay
		self.MaxAccumTime = config.MaxAccumTime * utils.HOUR_SECS // 配置表中是小时，转换为秒
		self.IsPublic = (config.IsPublic == 1)
		self.IsUnlocked = (level <= MINE_LEVEL_PRIVATE_MAX) // 1-3级默认解锁
	} else {
		// 如果没有配置，使用默认值
		self.IsPublic = level >= MINE_LEVEL_PUBLIC_MIN

		// 设置解锁天数
		switch level {
		case 1, 2, 3:
			self.UnlockDay = 1 // 1-3级矿脉第一天就解锁
			self.IsUnlocked = true
		case 4:
			self.UnlockDay = 2 // 4级矿脉第2天解锁
		case 5:
			self.UnlockDay = 3 // 5级矿脉第3天解锁
		case 6:
			self.UnlockDay = 4 // 6级矿脉第4天解锁
		case 7:
			self.UnlockDay = 5 // 7级矿脉第5天解锁
		}

		// 设置最大累积时间(根据等级不同)
		switch level {
		case 1, 2:
			self.MaxAccumTime = 4 * utils.HOUR_SECS // 4小时
		case 3, 4:
			self.MaxAccumTime = 6 * utils.HOUR_SECS // 6小时
		case 5, 6:
			self.MaxAccumTime = 8 * utils.HOUR_SECS // 8小时
		case 7:
			self.MaxAccumTime = 12 * utils.HOUR_SECS // 12小时
		}
	}

	// 设置区域(1-4，对应四个角落)
	self.Region = int((mineId % 4) + 1)

	// 初始化其他属性
	self.BonusActive = false

	// 初始化护盾相关属性
	self.ShieldActive = false
	self.ShieldStartTime = 0
	self.ShieldEndTime = 0
	self.ShieldDuration = 0
}

func (self *MineWarMgr) NewMineWarInfo() *MineWarInfo {
	data := new(MineWarInfo)
	data.MineWarMines = make(map[int]map[int64]*MineWarMine, 0)
	data.MineWarPlayer = make(map[int]map[int64]*MineWarPlayer, 0)
	data.MineWarServer = make(map[int]map[int]*MineWarServer, 0)
	data.MineWarServerGroup = make(map[int]int)
	data.lock = new(sync.RWMutex)
	return data
}

// ! 从数据库载入数据
func (self *MineWarMgr) GetAllData() {
	//计算赛季刷新时间
	if self.MineWarInfo == nil {
		self.MineWarInfo = self.NewMineWarInfo()
	}

	if self.MineWarInfo.MineWarConfig == nil {
		self.MineWarInfo.MineWarConfig = new(MineWarConfig)
		queryConfigStr := fmt.Sprintf("select * from `%s` limit 1;", "tbl_minewarconfig")
		ret := db.GetDBMgr().DBUser.GetOneData(queryConfigStr, self.MineWarInfo.MineWarConfig, "tbl_minewarconfig", 0)
		if ret == true {
			self.MineWarInfo.MineWarConfig.Decode()
		} else {
			self.MineWarInfo.MineWarConfig.Id = 1
			db.InsertTable("tbl_minewarconfig", self.MineWarInfo.MineWarConfig, 0, false)
		}
		self.MineWarInfo.MineWarConfig.Init("tbl_minewarconfig", self.MineWarInfo.MineWarConfig, false)
	}

	if self.MineWarInfo.MineWarConfig.EndTime == 0 {
		return
	}

	queryStr := fmt.Sprintf("select * from `tbl_minewaruser`;")
	var msg MineWarMine
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*MineWarMine)
		_, ok := self.MineWarInfo.MineWarMines[data.GroupId]
		if !ok {
			self.MineWarInfo.MineWarMines[data.GroupId] = make(map[int64]*MineWarMine)
		}

		self.MineWarInfo.MineWarMines[data.GroupId][data.MineId] = data
		data.Init("tbl_minewaruser", data, false)

		if data.SeasonTime != self.MineWarInfo.MineWarConfig.EndTime {
			data.FightInfo = ""
			data.SeasonTime = self.MineWarInfo.MineWarConfig.EndTime
		}
		data.Decode()

		if data != nil && data.fightInfo != nil {
			// TODO: 添加服务器列表和玩家管理逻辑
		}
	}
	return
}

func (self *MineWarMgr) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.EnterMinePoint(req, res)
}

func (self *MineWarMgr) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.AttackMinePoint(req, res)
}

func (self *MineWarMgr) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.LeaveMinePoint(req, res)
}

func (self *MineWarMgr) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.GetMineOutput(req, res)
}

// 处理挑战次数相关RPC
func (self *MineWarMgr) HandleMineChallenge(req *RPC_MineChallengeReq, res *RPC_MineChallengeRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.HandleMineChallenge(req, res)
}

// 处理护盾相关RPC
func (self *MineWarMgr) HandleMineShield(req *RPC_MineShieldReq, res *RPC_MineShieldRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.HandleMineShield(req, res)
}

func (self *MineWarMgr) Update() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.Update()
	}
}

// ! 主逻辑循环
func (self *MineWarMgr) Run() {
	//! 每秒执行一次
	ticker := time.NewTicker(time.Minute * 1)
	for {
		<-ticker.C
		self.OnTimer()
	}
	ticker.Stop()
}

// ! 主逻辑循环
func (self *MineWarMgr) OnTimer() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.lock.Lock()
		defer self.MineWarInfo.lock.Unlock()
		self.MineWarInfo.OnTimer()
	}
}

func (self *MineWarMgr) IsWarTime() bool {
	t := time.Now()

	// 检查是否在活动期间(周二到周日)
	if t.Weekday() < time.Tuesday || t.Weekday() > time.Sunday {
		return false // 不在活动期间
	}

	// 如果是周日，检查是否已过结算时间
	if t.Weekday() == time.Sunday && t.Hour() >= SETTLEMENT_HOUR {
		return false // 活动已结束
	}

	// 检查是否在休赛期(每天23:00到次日12:00)
	if t.Hour() >= REST_START_HOUR || t.Hour() < REST_END_HOUR {
		return false // 在休赛期，无法战斗
	}

	return true // 可以战斗
}

// 检查是否在休赛期
func (self *MineWarMgr) IsRestPeriod() bool {
	t := time.Now()
	return t.Hour() >= REST_START_HOUR || t.Hour() < REST_END_HOUR
}

// 检查是否在活动期间
func (self *MineWarMgr) IsActivityPeriod() bool {
	t := time.Now()

	// 检查是否在活动期间(周二到周日)
	if t.Weekday() < time.Tuesday || t.Weekday() > time.Sunday {
		return false
	}

	// 如果是周日，检查是否已过结算时间
	if t.Weekday() == time.Sunday && t.Hour() >= SETTLEMENT_HOUR {
		return false
	}

	return true
}

// 获取分组ID
func (self *MineWarMgr) GetGroupId(serverId int, keyId int) int {
	// 简单的分组逻辑，可以根据需要调整
	return keyId
}

// 处理队伍解锁相关RPC
func (self *MineWarMgr) HandleMineTeamUnlock(req *RPC_MineTeamUnlockReq, res *RPC_MineTeamUnlockRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.HandleMineTeamUnlock(req, res)
}

// 处理积分查询相关RPC
func (self *MineWarMgr) HandleMineScore(req *RPC_MineScoreReq, res *RPC_MineScoreRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.HandleMineScore(req, res)
}
