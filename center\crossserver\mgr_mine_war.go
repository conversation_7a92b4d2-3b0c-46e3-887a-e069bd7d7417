package crossserver

import (
	"fmt"
	"master/db"
	"master/model"
	"master/utils"
	"sync"
	"time"

	json "github.com/bytedance/sonic"
)

const (
	MINE_WAR_REDIS_FIGHT_RECORD = "minewarfightrecord"
	MINE_WAR_REDIS_RECORD       = "minewarrecord"
	MINE_WAR_REDIS_FIGHT_ID     = "minewarfightid"
	MINE_WAR_REDIS_RECORD_NUM   = 20
	MINE_WAR_ATTACK_CD          = 90
	MINE_WAR_STATUS_PREPARE     = 1 // 准备期
	MINE_WAR_STATUS_BATTLE      = 2 // 战斗期
	MINE_WAR_STATUS_RESULT      = 3 // 结算期
)

const (
	ACTION_ENTER  = 1
	ACTION_LEAVE  = 2
	ACTION_ATTACK = 3

	MINE_LEVEL_MAX = 7  // 矿点等级上限(1-7级)
	POSITION_MAX   = 16 // 坑位上限
	TEAM_MAX       = 3  // 队伍上限

	// 时间常量
	WEEKLY_START_DAY  = 2  // 每周开始日(周二)
	ACTIVITY_DURATION = 6  // 活动持续天数
	SETTLEMENT_HOUR   = 20 // 结算时间(20:00)
	SETTLEMENT_DAY    = 0  // 结算日(周日)
	REST_START_HOUR   = 23 // 休赛期开始时间(23:00)
	REST_END_HOUR     = 12 // 休赛期结束时间(12:00)

	// 矿脉等级常量
	MINE_LEVEL_PRIVATE_MAX = 3 // 私有矿脉最高等级(1-3级)
	MINE_LEVEL_PUBLIC_MIN  = 4 // 公共矿脉最低等级(4级)
	MINE_LEVEL_PUBLIC_MAX  = 7 // 公共矿脉最高等级(7级)

	// 产出效率
	REST_PERIOD_OUTPUT_RATE = 50 // 休赛期产出效率(50%)
)

type MineWarConfig struct {
	Id               int
	KeyId            int
	StartTime        int64 // 开始时间
	PrepareTime      int64 // 准备期
	BattleTime       int64 // 战斗期
	ResultTime       int64 // 结算期
	EndTime          int64 // 总结束时间
	KeepTime         int64 `json:"keep_time"`         // 持续时间
	BoothProtection  int64 `json:"booth_protection"`  // 占位保护时长秒
	AttackNum        int   `json:"attack_num"`        // 每日攻击次数
	ShieldProtection int64 `json:"shield_protection"` // 护盾保护时长秒
	BoothMaxtime     int64 `json:"booth_maxtime"`     // 坑位最长占领时间
	AttackCd         int64 `json:"attack_cd"`         // 队伍攻击CD时间

	db.DataUpdate
}

type MineWarUpdateInfo struct {
	Point  int64   `json:"point"`
	TeamId []int64 `json:"team_id"`
}

func (self *MineWarConfig) Decode() {
}

// ! 将data数据写入数据库
func (self *MineWarConfig) Encode() {
}

// 矿脉等级配置表
type MineWarLevelConfig struct {
	Level          int   `json:"level"`            // 矿脉等级
	UnlockDay      int   `json:"unlock_day"`       // 解锁天数
	MaxAccumTime   int64 `json:"max_accum_time"`   // 最大累积时间(小时)
	BaseOutputRate int64 `json:"base_output_rate"` // 基础产出率(每秒)
	Shield         int64 `json:"shield"`           // 护盾值
	AttackCd       int64 `json:"attack_cd"`        // 攻击冷却时间(秒)
	MaxAttackNum   int   `json:"max_attack_num"`   // 每日最大攻击次数
	IsPublic       int   `json:"is_public"`        // 是否为公共矿脉(0:私有 1:公共)
	MineCount      int   `json:"mine_count"`       // 该等级矿脉数量
}

// 矿脉活动配置表
type MineWarActivityConfig struct {
	ActivityGroup  int `json:"activity_group"`   // 活动组ID
	KeepTime       int `json:"keep_time"`        // 活动持续天数
	StartDay       int `json:"start_day"`        // 开始日期(周几)
	SettlementHour int `json:"settlement_hour"`  // 结算时间(小时)
	RestStartHour  int `json:"rest_start_hour"`  // 休赛期开始时间
	RestEndHour    int `json:"rest_end_hour"`    // 休赛期结束时间
	RestOutputRate int `json:"rest_output_rate"` // 休赛期产出效率(百分比)
	TeamMaxNum     int `json:"team_max_num"`     // 玩家最大队伍数量
	BonusRate      int `json:"bonus_rate"`       // 5级矿脉加成比例(百分比)
}

// 矿脉产出配置表
type MineWarOutputConfig struct {
	Level       int `json:"level"`        // 矿脉等级
	OutputItem1 int `json:"output_item1"` // 产出物品1ID(商店货币)
	OutputNum1  int `json:"output_num1"`  // 产出物品1数量
	OutputItem2 int `json:"output_item2"` // 产出物品2ID(治疗妖姬)
	OutputNum2  int `json:"output_num2"`  // 产出物品2数量
	OutputItem3 int `json:"output_item3"` // 产出物品3ID(积分)
	OutputNum3  int `json:"output_num3"`  // 产出物品3数量
}

type JS_MineInfo struct {
	MineId     int64 `json:"mine_id"`
	TeamType   int   `json:"team_type"`
	StartTime  int64 `json:"start_time"`  // 占领起始时间
	ShieldTime int64 `json:"shield_time"` // 护盾保护起始时间
}

type MineWarMine struct {
	MineId     int64
	GroupId    int // 预留字段 暂不启用 如果以后要做服务器分组的话不用额外加字段了
	SeasonTime int64
	FightInfo  string
	StartTime  int64 // 占领起始时间
	AttackTime int64 // 攻击者的攻击时间
	AttackUid  int64 // 攻击者的uid
	ShieldTime int64 // 护盾保护起始时间

	// 新增字段
	Level        int   `json:"level"`          // 矿脉等级
	ServerId     int   `json:"server_id"`      // 所属服务器ID(1-3级矿脉专属)
	Region       int   `json:"region"`         // 区域ID(1-4，对应四个角落)
	IsPublic     bool  `json:"is_public"`      // 是否为公共矿脉(4-7级)
	UnlockDay    int   `json:"unlock_day"`     // 解锁天数
	IsUnlocked   bool  `json:"is_unlocked"`    // 是否已解锁
	BonusActive  bool  `json:"bonus_active"`   // 是否有产出加成(5级矿脉随机)
	MaxAccumTime int64 `json:"max_accum_time"` // 最大累积时间(秒)

	fightInfo *model.JS_FightInfo
	db.DataUpdate
}

// ! 将数据库数据写入data-无特殊结构
func (self *MineWarMine) Decode() {
	json.Unmarshal([]byte(self.FightInfo), &self.fightInfo)
}

// ! 将data数据写入数据库
func (self *MineWarMine) Encode() {
	self.FightInfo = utils.HF_JtoA(&self.fightInfo)
}

type MineWarServer struct {
	ServerId   int    `json:"server_id"`
	ServerName string `json:"server_name"`
}

type MineWarPlayer struct {
	Uid      int64           `json:"uid"`
	ServerId int             `json:"server_id"`
	TeamMine map[int64]int64 `json:"team_mine"` // map[队伍id][矿点id]
}

func (self *MineWarInfo) JS_MineInfoChange(mine *MineWarMine) *JS_MineInfo {
	mineInfo := new(JS_MineInfo)
	mineInfo.MineId = mine.MineId
	mineInfo.TeamType = int(mine.fightInfo.Param)
	mineInfo.StartTime = mine.StartTime
	mineInfo.ShieldTime = mine.ShieldTime
	return mineInfo
}

type RPC_MinePointReq struct {
	MineId      int64 `json:"mine_id"`
	Uid         int64 `json:"uid"`
	ServerId    int64 `json:"server_id"`
	TeamId      int64 `json:"team_id"`
	Position    int   `json:"position"`
	AttackPower int64 `json:"attack_power"`
	GroupId     int   `json:"group_id"` // 支线ID
}

type RPC_MinePointRes struct {
	RetCode     int              `json:"ret_code"`
	Points      []*MinePoint     `json:"points"`
	Positions   []*MinePosition  `json:"positions"`
	Teams       []*MineTeam      `json:"teams"`
	OutputNum   int              `json:"output_num"`
	OutputItem  int              `json:"output_item"`
	OutputItems []MineOutputItem `json:"output_items"` // 多种产出物品
}

type MinePoint struct {
	MineId       int64 `json:"mine_id"`
	OwnerId      int64 `json:"owner_id"`
	Shield       int64 `json:"shield"`
	AttackNum    int   `json:"attack_num"`
	OutputItem   int   `json:"output_item"`
	OutputRate   int64 `json:"output_rate"`
	Level        int   `json:"level"`
	LineIndex    int   `json:"line_index"`
	GroupId      int   `json:"group_id"`       // 支线ID，0表示公共矿点
	ServerId     int   `json:"server_id"`      // 所属服务器ID(1-3级矿脉专属)
	Region       int   `json:"region"`         // 区域ID(1-4，对应四个角落)
	IsPublic     bool  `json:"is_public"`      // 是否为公共矿脉(4-7级)
	UnlockDay    int   `json:"unlock_day"`     // 解锁天数
	IsUnlocked   bool  `json:"is_unlocked"`    // 是否已解锁
	BonusActive  bool  `json:"bonus_active"`   // 是否有产出加成(5级矿脉随机)
	MaxAccumTime int64 `json:"max_accum_time"` // 最大累积时间(秒)
	LastSaveTime int64 `json:"last_save_time"`
	OutputNum    int64 `json:"output_num"`
}

type MinePosition struct {
	PositionId int64 `json:"position_id"`
	MineId     int64 `json:"mine_id"`
	PlayerId   int64 `json:"player_id"`
	EnterTime  int64 `json:"enter_time"`
	LeaveTime  int64 `json:"leave_time"`
	Status     int   `json:"status"`
	GroupId    int   `json:"group_id"`
}

type MineTeam struct {
	TeamId         int64 `json:"team_id"`
	PlayerId       int64 `json:"player_id"`
	MineId         int64 `json:"mine_id"`
	PositionId     int64 `json:"position_id"`
	GroupId        int   `json:"group_id"`
	Status         int   `json:"status"`
	LastAttackTime int64 `json:"last_attack_time"`
}

type MineWarInfo struct {
	MineWarMines       map[int]map[int64]*MineWarMine   // 矿点数据 map[group][mineid]
	MineWarPlayer      map[int]map[int64]*MineWarPlayer // 玩家数据 map[group][uid]
	MineWarServer      map[int]map[int]*MineWarServer   // 参与的服务器数据 map[group][serverid]
	MineWarServerGroup map[int]int                      // 服务器所在的组 map[serverid]group

	MineWarConfig *MineWarConfig
	lock          *sync.RWMutex
}

// ! 矿战管理类
type MineWarMgr struct {
	MineWarInfo *MineWarInfo //!

	MineWarConfigMap         map[int64]*MineWarConfig       //Mine_War_Config
	MineWarLevelConfigMap    map[int]*MineWarLevelConfig    //Mine_War_Level_Config
	MineWarActivityConfigMap map[int]*MineWarActivityConfig //Mine_War_Activity_Config
	MineWarOutputConfigMap   map[int]*MineWarOutputConfig   //Mine_War_Output_Config
}

func (self *MineWarMgr) Save() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.Save()
	}
}

func (self *MineWarInfo) Save() {
	self.lock.RLock()
	defer self.lock.RUnlock()
	for _, group := range self.MineWarMines {
		for _, v := range group {
			v.Encode()
			v.UpdateEx("groupid", v.GroupId)
		}
	}
	self.MineWarConfig.Update(true, false)
}

func (self *MineWarInfo) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 检查是否在活动期间
	if !GetMineWarMgr().IsActivityPeriod() {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查玩家队伍数量是否已满
	if !self.CanPlayerAddTeam(req.Uid) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否可以攻击该矿脉
	if !self.CanAttackMine(int(req.ServerId), mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// TODO: 实现具体的进入矿点逻辑
	res.RetCode = RETCODE_OK
}

// 检查玩家是否可以添加新队伍
func (self *MineWarInfo) CanPlayerAddTeam(uid int64) bool {
	// 从配置表获取最大队伍数量
	maxTeams := TEAM_MAX // 默认值
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxTeams = activityConfig.TeamMaxNum
		}
	}

	// 统计玩家当前的队伍数量
	teamCount := 0
	for _, group := range self.MineWarPlayer {
		if player, ok := group[uid]; ok {
			teamCount += len(player.TeamMine)
		}
	}

	return teamCount < maxTeams
}

// 获取矿脉信息
func (self *MineWarInfo) GetMineWarMine(groupId int, mineId int64) (*MineWarMine, bool) {
	if group, ok := self.MineWarMines[groupId]; ok {
		if mine, ok := group[mineId]; ok {
			return mine, true
		}
	}
	return nil, false
}

// 检查队伍是否可以更换阵容
func (self *MineWarInfo) CanChangeTeamFormation(uid int64, teamId int64) bool {
	// 检查队伍是否正在占据矿脉
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil && mine.fightInfo.Uid == uid && mine.fightInfo.Param == teamId {
				return false // 正在占据矿脉，无法更换阵容
			}
		}
	}
	return true
}

func (self *MineWarInfo) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// TODO: 实现攻击矿点逻辑
	res.RetCode = RETCODE_OK
}

func (self *MineWarInfo) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// TODO: 实现离开矿点逻辑
	res.RetCode = RETCODE_OK
}

func (self *MineWarInfo) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否为占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != req.Uid {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 计算产出
	currentTime := time.Now().Unix()
	outputTime := currentTime - mine.StartTime

	// 限制最大累积时间
	if outputTime > mine.MaxAccumTime {
		outputTime = mine.MaxAccumTime
	}

	// 计算基础产出
	baseRate := self.GetMineBaseOutputRate(mine.Level)
	actualRate := self.GetOutputRate(baseRate)

	// 如果有加成，增加产出
	if mine.BonusActive {
		bonusRate := 150 // 默认50%加成
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				bonusRate = 100 + activityConfig.BonusRate
			}
		}
		actualRate = actualRate * int64(bonusRate) / 100
	}

	totalOutput := outputTime * actualRate

	// 更新时间
	mine.StartTime = currentTime

	// TODO: 添加到玩家背包和积分

	res.RetCode = RETCODE_OK
	res.OutputNum = int(totalOutput)
	res.OutputItems = self.GetMineOutputItems(mine.Level)
}

// 获取矿脉基础产出率
func (self *MineWarInfo) GetMineBaseOutputRate(level int) int64 {
	config := GetMineWarMgr().GetMineWarLevelConfig(level)
	if config != nil {
		return config.BaseOutputRate
	}

	// 如果没有配置，使用默认值
	switch level {
	case 1:
		return 10 // 每秒10个货币
	case 2:
		return 15
	case 3:
		return 20
	case 4:
		return 30
	case 5:
		return 45
	case 6:
		return 60
	case 7:
		return 100
	default:
		return 10
	}
}

// 获取矿脉产出配置
func (self *MineWarInfo) GetMineOutputItems(level int) []MineOutputItem {
	config := GetMineWarMgr().GetMineWarOutputConfig(level)
	if config != nil {
		items := make([]MineOutputItem, 0)
		if config.OutputItem1 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem1, ItemNum: config.OutputNum1})
		}
		if config.OutputItem2 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem2, ItemNum: config.OutputNum2})
		}
		if config.OutputItem3 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem3, ItemNum: config.OutputNum3})
		}
		return items
	}

	// 如果没有配置，返回默认产出
	return []MineOutputItem{
		{ItemId: 10001 + level, ItemNum: 1}, // 示例物品ID
	}
}

// 矿脉产出物品结构
type MineOutputItem struct {
	ItemId  int `json:"item_id"`
	ItemNum int `json:"item_num"`
}

// 更新玩家积分
func (self *MineWarInfo) UpdatePlayerScore(uid int64, serverId int, score int64) {
	// TODO: 实现积分更新逻辑
	// 需要调用排行榜系统更新个人积分、公会积分、服务器积分
}

func (self *MineWarInfo) Update() {
	// TODO: 实现更新逻辑
}

func (self *MineWarInfo) OnTimer() {
	// TODO: 实现定时器逻辑
	self.CheckMineUnlock()
	self.UpdateDailyBonus()
	self.CheckWeeklySettlement()
}

// 检查周结算
func (self *MineWarInfo) CheckWeeklySettlement() {
	t := time.Now()

	// 检查是否为周日20:00
	if t.Weekday() != time.Sunday || t.Hour() != SETTLEMENT_HOUR {
		return
	}

	// 执行结算
	self.DoWeeklySettlement()
}

// 执行周结算
func (self *MineWarInfo) DoWeeklySettlement() {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 计算最终排名
	guildRanking := self.CalculateGuildRanking()
	playerRanking := self.CalculatePlayerRanking()
	serverRanking := self.CalculateServerRanking()

	// 发放奖励
	self.DistributeGuildRewards(guildRanking)
	self.DistributePlayerRewards(playerRanking)
	self.DistributeServerRewards(serverRanking)

	// 清理数据，准备下一轮
	self.PrepareNextSeason()
}

// 计算公会排名
func (self *MineWarInfo) CalculateGuildRanking() []GuildRankInfo {
	// TODO: 实现公会积分排名逻辑
	return make([]GuildRankInfo, 0)
}

// 计算个人排名
func (self *MineWarInfo) CalculatePlayerRanking() []PlayerRankInfo {
	// TODO: 实现个人积分排名逻辑
	return make([]PlayerRankInfo, 0)
}

// 计算服务器排名
func (self *MineWarInfo) CalculateServerRanking() []ServerRankInfo {
	// TODO: 实现服务器积分排名逻辑
	return make([]ServerRankInfo, 0)
}

// 发放公会奖励
func (self *MineWarInfo) DistributeGuildRewards(ranking []GuildRankInfo) {
	// TODO: 实现公会奖励发放逻辑
}

// 发放个人奖励
func (self *MineWarInfo) DistributePlayerRewards(ranking []PlayerRankInfo) {
	// TODO: 实现个人奖励发放逻辑
}

// 发放服务器奖励
func (self *MineWarInfo) DistributeServerRewards(ranking []ServerRankInfo) {
	// TODO: 实现服务器奖励发放逻辑
}

// 准备下一赛季
func (self *MineWarInfo) PrepareNextSeason() {
	// TODO: 清理数据，重置状态，准备下一轮活动
}

// 排名信息结构体
type GuildRankInfo struct {
	GuildId int64 `json:"guild_id"`
	Score   int64 `json:"score"`
	Rank    int   `json:"rank"`
}

type PlayerRankInfo struct {
	Uid   int64 `json:"uid"`
	Score int64 `json:"score"`
	Rank  int   `json:"rank"`
}

type ServerRankInfo struct {
	ServerId int   `json:"server_id"`
	Score    int64 `json:"score"`
	Rank     int   `json:"rank"`
}

// 检查矿脉解锁
func (self *MineWarInfo) CheckMineUnlock() {
	if !GetMineWarMgr().IsActivityPeriod() {
		return
	}

	// 计算活动开始后的天数
	activityDay := self.GetActivityDay()

	// 检查所有矿脉的解锁状态
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if !mine.IsUnlocked && activityDay >= mine.UnlockDay {
				mine.IsUnlocked = true
				// TODO: 发送解锁通知
			}
		}
	}
}

// 更新每日加成(5级矿脉随机获得产出加成)
func (self *MineWarInfo) UpdateDailyBonus() {
	t := time.Now()
	// 每天0点更新加成
	if t.Hour() != 0 || t.Minute() != 0 {
		return
	}

	// 重置所有5级矿脉的加成状态
	level5Mines := make([]*MineWarMine, 0)
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.Level == 5 && mine.IsUnlocked {
				mine.BonusActive = false
				level5Mines = append(level5Mines, mine)
			}
		}
	}

	// 随机选择一个5级矿脉获得加成
	if len(level5Mines) > 0 {
		randomIndex := utils.HF_RandInt(0, len(level5Mines)-1)
		level5Mines[randomIndex].BonusActive = true
		// TODO: 发送加成通知
	}
}

// 获取活动开始后的天数
func (self *MineWarInfo) GetActivityDay() int {
	if self.MineWarConfig == nil {
		return 0
	}

	now := time.Now().Unix()
	startTime := self.MineWarConfig.StartTime
	daysPassed := (now - startTime) / utils.DAY_SECS

	return int(daysPassed) + 1 // 第一天为第1天
}

// 检查矿脉是否可以攻击
func (self *MineWarInfo) CanAttackMine(attackerServerId int, targetMine *MineWarMine) bool {
	// 检查是否在战斗时间
	if !GetMineWarMgr().IsWarTime() {
		return false
	}

	// 检查矿脉是否已解锁
	if !targetMine.IsUnlocked {
		return false
	}

	// 1-3级矿脉只能本服务器攻击
	if targetMine.Level <= MINE_LEVEL_PRIVATE_MAX {
		return targetMine.ServerId == attackerServerId
	}

	// 4-7级矿脉为公共矿脉，所有服务器都可以攻击
	return true
}

// 计算产出效率(休赛期降低效率)
func (self *MineWarInfo) GetOutputRate(baseRate int64) int64 {
	if GetMineWarMgr().IsRestPeriod() {
		// 从配置表获取休赛期产出效率
		restRate := REST_PERIOD_OUTPUT_RATE // 默认50%
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				restRate = activityConfig.RestOutputRate
			}
		}
		return baseRate * int64(restRate) / 100
	}
	return baseRate
}

// ! 矿战管理类
var s_mineWarMgr *MineWarMgr = nil

func GetMineWarMgr() *MineWarMgr {
	if s_mineWarMgr == nil {
		s_mineWarMgr = new(MineWarMgr)
		s_mineWarMgr.LoadCsv()
	}
	return s_mineWarMgr
}

func (self *MineWarMgr) LoadCsv() {
	self.MineWarConfigMap = make(map[int64]*MineWarConfig)

	// 加载矿脉等级配置
	self.MineWarLevelConfigMap = make(map[int]*MineWarLevelConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Level_Config", &self.MineWarLevelConfigMap)

	// 加载矿脉活动配置
	self.MineWarActivityConfigMap = make(map[int]*MineWarActivityConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Activity_Config", &self.MineWarActivityConfigMap)

	// 加载矿脉产出配置
	self.MineWarOutputConfigMap = make(map[int]*MineWarOutputConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Output_Config", &self.MineWarOutputConfigMap)

	return
}

// 获取矿脉等级配置
func (self *MineWarMgr) GetMineWarLevelConfig(level int) *MineWarLevelConfig {
	if config, ok := self.MineWarLevelConfigMap[level]; ok {
		return config
	}
	return nil
}

// 获取矿脉活动配置
func (self *MineWarMgr) GetMineWarActivityConfig(group int) *MineWarActivityConfig {
	if config, ok := self.MineWarActivityConfigMap[group]; ok {
		return config
	}
	return nil
}

// 获取矿脉产出配置
func (self *MineWarMgr) GetMineWarOutputConfig(level int) *MineWarOutputConfig {
	if config, ok := self.MineWarOutputConfigMap[level]; ok {
		return config
	}
	return nil
}

func (self *MineWarMgr) CheckMineWar(req *RPC_UpdateActivityReq) {
	if self.MineWarInfo.MineWarConfig.EndTime == 0 {
		self.MineWarInfo.InitMineWarConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.GetAllData()
	} else if self.MineWarInfo.MineWarConfig.EndTime != req.EndTime {
		self.MineWarInfo.InitMineWarConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.MineWarInfo.CleanMineWar()
	}
}

func (self *MineWarInfo) InitMineWarConfig(starttime, rewardtime, enttime int64, keyid int) {
	self.lock.Lock()
	defer self.lock.Unlock()
	self.MineWarConfig.KeyId = keyid
	self.MineWarConfig.StartTime = starttime
	self.MineWarConfig.EndTime = enttime

	// 从配置表获取活动配置
	activityConfig := GetMineWarMgr().GetMineWarActivityConfig(keyid / 1000)
	if activityConfig != nil {
		// 使用配置表中的时间设置
		self.MineWarConfig.PrepareTime = self.MineWarConfig.StartTime + 1*utils.DAY_SECS
		self.MineWarConfig.BattleTime = self.MineWarConfig.PrepareTime + int64(activityConfig.KeepTime)*utils.DAY_SECS
		self.MineWarConfig.ResultTime = self.MineWarConfig.BattleTime + 1*utils.DAY_SECS

		// 设置其他配置
		self.MineWarConfig.KeepTime = int64(activityConfig.KeepTime)
		self.MineWarConfig.AttackNum = activityConfig.TeamMaxNum
	} else {
		// 如果没有配置，使用默认值
		self.MineWarConfig.PrepareTime = self.MineWarConfig.StartTime + 1*utils.DAY_SECS
		self.MineWarConfig.BattleTime = self.MineWarConfig.PrepareTime + 5*utils.DAY_SECS
		self.MineWarConfig.ResultTime = self.MineWarConfig.BattleTime + 1*utils.DAY_SECS
	}
}

func (self *MineWarInfo) CleanMineWar() {
	self.lock.Lock()
	defer self.lock.Unlock()
	for _, group := range self.MineWarMines {
		for _, v := range group {
			if v.SeasonTime != self.MineWarConfig.EndTime {
				v.SeasonTime = self.MineWarConfig.EndTime
				v.fightInfo = NewMineWarFightInfo(v.MineId)
				v.StartTime = 0
				v.ShieldTime = 0
				v.AttackUid = 0
				v.AttackTime = 0

				// 重新初始化矿脉属性
				v.InitMineProperties(v.MineId)
				v.Decode()
			}
		}
	}
	self.MineWarPlayer = make(map[int]map[int64]*MineWarPlayer)
	self.MineWarServer = make(map[int]map[int]*MineWarServer)
	self.MineWarServerGroup = make(map[int]int)
}

// 初始化服务器矿脉分配
func (self *MineWarInfo) InitServerMineAllocation(groupId int, serverIds []int) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 确保组存在
	if _, ok := self.MineWarMines[groupId]; !ok {
		self.MineWarMines[groupId] = make(map[int64]*MineWarMine)
	}

	// 为每个服务器分配1-3级矿脉到四个角落
	for i, serverId := range serverIds {
		region := (i % 4) + 1 // 分配到4个区域

		// 为每个服务器在指定区域创建1-3级矿脉
		for level := 1; level <= MINE_LEVEL_PRIVATE_MAX; level++ {
			// 生成矿脉ID: 服务器ID * 1000 + 区域 * 100 + 等级
			mineId := int64(serverId*1000 + region*100 + level)

			mine := NewMineWarUser(mineId, groupId)
			mine.ServerId = serverId
			mine.Region = region
			mine.Level = level
			mine.IsPublic = false
			mine.IsUnlocked = true // 1-3级矿脉默认解锁

			self.MineWarMines[groupId][mineId] = mine
		}
	}

	// 创建公共矿脉(4-7级)
	self.InitPublicMines(groupId)
}

// 初始化公共矿脉
func (self *MineWarInfo) InitPublicMines(groupId int) {
	// 创建4-7级公共矿脉，分布在地图中央区域
	for level := MINE_LEVEL_PUBLIC_MIN; level <= MINE_LEVEL_PUBLIC_MAX; level++ {
		// 从配置表获取矿脉数量
		mineCount := level - 2 // 默认值：4级2个，5级3个，6级4个，7级5个
		config := GetMineWarMgr().GetMineWarLevelConfig(level)
		if config != nil && config.MineCount > 0 {
			mineCount = config.MineCount
		}

		for i := 1; i <= mineCount; i++ {
			// 生成公共矿脉ID: 9000000 + 等级 * 1000 + 序号
			mineId := int64(9000000 + level*1000 + i)

			mine := NewMineWarUser(mineId, groupId)
			mine.ServerId = 0 // 公共矿脉不属于特定服务器
			mine.Region = 0   // 公共区域
			mine.Level = level
			mine.IsPublic = true
			mine.IsUnlocked = (level == 4) // 4级矿脉默认解锁，其他需要等待

			self.MineWarMines[groupId][mineId] = mine
		}
	}
}

// 根据配置表初始化所有矿脉
func (self *MineWarInfo) InitMinesFromConfig(groupId int, serverIds []int) {
	// 确保组存在
	if _, ok := self.MineWarMines[groupId]; !ok {
		self.MineWarMines[groupId] = make(map[int64]*MineWarMine)
	}

	// 初始化所有等级的矿脉
	for level := 1; level <= MINE_LEVEL_MAX; level++ {
		config := GetMineWarMgr().GetMineWarLevelConfig(level)
		if config == nil {
			continue
		}

		if config.IsPublic == 0 {
			// 私有矿脉，为每个服务器创建
			for i, serverId := range serverIds {
				region := (i % 4) + 1 // 分配到4个区域

				for j := 1; j <= config.MineCount; j++ {
					// 生成矿脉ID: 服务器ID * 10000 + 等级 * 1000 + 序号
					mineId := int64(serverId*10000 + level*1000 + j)

					mine := NewMineWarUser(mineId, groupId)
					mine.ServerId = serverId
					mine.Region = region
					mine.Level = level
					mine.IsPublic = false
					mine.IsUnlocked = (level <= MINE_LEVEL_PRIVATE_MAX) // 1-3级默认解锁

					self.MineWarMines[groupId][mineId] = mine
				}
			}
		} else {
			// 公共矿脉
			for i := 1; i <= config.MineCount; i++ {
				// 生成公共矿脉ID: 9000000 + 等级 * 1000 + 序号
				mineId := int64(9000000 + level*1000 + i)

				mine := NewMineWarUser(mineId, groupId)
				mine.ServerId = 0 // 公共矿脉不属于特定服务器
				mine.Region = 0   // 公共区域
				mine.Level = level
				mine.IsPublic = true
				mine.IsUnlocked = (level == 4) // 4级矿脉默认解锁，其他需要等待

				self.MineWarMines[groupId][mineId] = mine
			}
		}
	}
}

func NewMineWarFightInfo(id int64) *model.JS_FightInfo {
	data := new(model.JS_FightInfo)
	data.Rankid = int(id)
	return data
}

func NewMineWarUser(mineId int64, groupid int) *MineWarMine {
	data := new(MineWarMine)
	data.MineId = mineId
	data.GroupId = groupid
	data.SeasonTime = GetMineWarMgr().MineWarInfo.MineWarConfig.EndTime
	data.fightInfo = NewMineWarFightInfo(mineId)
	data.StartTime = 0

	// 根据矿点ID初始化矿脉属性
	data.InitMineProperties(mineId)

	db.InsertTable("tbl_minewaruser", data, 0, false)
	data.Init("tbl_minewaruser", data, false)
	return data
}

// 初始化矿脉属性
func (self *MineWarMine) InitMineProperties(mineId int64) {
	// 根据矿点ID确定等级和属性
	// 这里简化处理，实际应该从配置表读取
	level := int((mineId % 7) + 1) // 1-7级
	self.Level = level

	// 从配置表获取矿脉等级配置
	config := GetMineWarMgr().GetMineWarLevelConfig(level)
	if config != nil {
		self.UnlockDay = config.UnlockDay
		self.MaxAccumTime = config.MaxAccumTime * utils.HOUR_SECS // 配置表中是小时，转换为秒
		self.IsPublic = (config.IsPublic == 1)
		self.IsUnlocked = (level <= MINE_LEVEL_PRIVATE_MAX) // 1-3级默认解锁
	} else {
		// 如果没有配置，使用默认值
		self.IsPublic = level >= MINE_LEVEL_PUBLIC_MIN

		// 设置解锁天数
		switch level {
		case 1, 2, 3:
			self.UnlockDay = 1 // 1-3级矿脉第一天就解锁
			self.IsUnlocked = true
		case 4:
			self.UnlockDay = 2 // 4级矿脉第2天解锁
		case 5:
			self.UnlockDay = 3 // 5级矿脉第3天解锁
		case 6:
			self.UnlockDay = 4 // 6级矿脉第4天解锁
		case 7:
			self.UnlockDay = 5 // 7级矿脉第5天解锁
		}

		// 设置最大累积时间(根据等级不同)
		switch level {
		case 1, 2:
			self.MaxAccumTime = 4 * utils.HOUR_SECS // 4小时
		case 3, 4:
			self.MaxAccumTime = 6 * utils.HOUR_SECS // 6小时
		case 5, 6:
			self.MaxAccumTime = 8 * utils.HOUR_SECS // 8小时
		case 7:
			self.MaxAccumTime = 12 * utils.HOUR_SECS // 12小时
		}
	}

	// 设置区域(1-4，对应四个角落)
	self.Region = int((mineId % 4) + 1)

	// 初始化其他属性
	self.BonusActive = false
}

func (self *MineWarMgr) NewMineWarInfo() *MineWarInfo {
	data := new(MineWarInfo)
	data.MineWarMines = make(map[int]map[int64]*MineWarMine, 0)
	data.MineWarPlayer = make(map[int]map[int64]*MineWarPlayer, 0)
	data.MineWarServer = make(map[int]map[int]*MineWarServer, 0)
	data.MineWarServerGroup = make(map[int]int)
	data.lock = new(sync.RWMutex)
	return data
}

// ! 从数据库载入数据
func (self *MineWarMgr) GetAllData() {
	//计算赛季刷新时间
	if self.MineWarInfo == nil {
		self.MineWarInfo = self.NewMineWarInfo()
	}

	if self.MineWarInfo.MineWarConfig == nil {
		self.MineWarInfo.MineWarConfig = new(MineWarConfig)
		queryConfigStr := fmt.Sprintf("select * from `%s` limit 1;", "tbl_minewarconfig")
		ret := db.GetDBMgr().DBUser.GetOneData(queryConfigStr, self.MineWarInfo.MineWarConfig, "tbl_minewarconfig", 0)
		if ret == true {
			self.MineWarInfo.MineWarConfig.Decode()
		} else {
			self.MineWarInfo.MineWarConfig.Id = 1
			db.InsertTable("tbl_minewarconfig", self.MineWarInfo.MineWarConfig, 0, false)
		}
		self.MineWarInfo.MineWarConfig.Init("tbl_minewarconfig", self.MineWarInfo.MineWarConfig, false)
	}

	if self.MineWarInfo.MineWarConfig.EndTime == 0 {
		return
	}

	queryStr := fmt.Sprintf("select * from `tbl_minewaruser`;")
	var msg MineWarMine
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*MineWarMine)
		_, ok := self.MineWarInfo.MineWarMines[data.GroupId]
		if !ok {
			self.MineWarInfo.MineWarMines[data.GroupId] = make(map[int64]*MineWarMine)
		}

		self.MineWarInfo.MineWarMines[data.GroupId][data.MineId] = data
		data.Init("tbl_minewaruser", data, false)

		if data.SeasonTime != self.MineWarInfo.MineWarConfig.EndTime {
			data.FightInfo = ""
			data.SeasonTime = self.MineWarInfo.MineWarConfig.EndTime
		}
		data.Decode()

		if data != nil && data.fightInfo != nil {
			// TODO: 添加服务器列表和玩家管理逻辑
		}
	}
	return
}

func (self *MineWarMgr) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.EnterMinePoint(req, res)
}

func (self *MineWarMgr) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.AttackMinePoint(req, res)
}

func (self *MineWarMgr) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.LeaveMinePoint(req, res)
}

func (self *MineWarMgr) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.GetMineOutput(req, res)
}

func (self *MineWarMgr) Update() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.Update()
	}
}

// ! 主逻辑循环
func (self *MineWarMgr) Run() {
	//! 每秒执行一次
	ticker := time.NewTicker(time.Minute * 1)
	for {
		<-ticker.C
		self.OnTimer()
	}
	ticker.Stop()
}

// ! 主逻辑循环
func (self *MineWarMgr) OnTimer() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.lock.Lock()
		defer self.MineWarInfo.lock.Unlock()
		self.MineWarInfo.OnTimer()
	}
}

func (self *MineWarMgr) IsWarTime() bool {
	t := time.Now()

	// 检查是否在活动期间(周二到周日)
	if t.Weekday() < time.Tuesday || t.Weekday() > time.Sunday {
		return false // 不在活动期间
	}

	// 如果是周日，检查是否已过结算时间
	if t.Weekday() == time.Sunday && t.Hour() >= SETTLEMENT_HOUR {
		return false // 活动已结束
	}

	// 检查是否在休赛期(每天23:00到次日12:00)
	if t.Hour() >= REST_START_HOUR || t.Hour() < REST_END_HOUR {
		return false // 在休赛期，无法战斗
	}

	return true // 可以战斗
}

// 检查是否在休赛期
func (self *MineWarMgr) IsRestPeriod() bool {
	t := time.Now()
	return t.Hour() >= REST_START_HOUR || t.Hour() < REST_END_HOUR
}

// 检查是否在活动期间
func (self *MineWarMgr) IsActivityPeriod() bool {
	t := time.Now()

	// 检查是否在活动期间(周二到周日)
	if t.Weekday() < time.Tuesday || t.Weekday() > time.Sunday {
		return false
	}

	// 如果是周日，检查是否已过结算时间
	if t.Weekday() == time.Sunday && t.Hour() >= SETTLEMENT_HOUR {
		return false
	}

	return true
}
