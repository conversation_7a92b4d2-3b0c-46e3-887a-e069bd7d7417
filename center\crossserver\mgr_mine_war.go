package crossserver

import (
	"fmt"
	"master/db"
	"master/model"
	"master/utils"
	"sync"
	"time"

	json "github.com/bytedance/sonic"
)

const (
	MINE_WAR_REDIS_FIGHT_RECORD = "minewarfightrecord"
	MINE_WAR_REDIS_RECORD       = "minewarrecord"
	MINE_WAR_REDIS_FIGHT_ID     = "minewarfightid"
	MINE_WAR_REDIS_RECORD_NUM   = 20
	MINE_WAR_ATTACK_CD          = 90
	MINE_WAR_STATUS_PREPARE     = 1 // 准备期
	MINE_WAR_STATUS_BATTLE      = 2 // 战斗期
	MINE_WAR_STATUS_RESULT      = 3 // 结算期
)

const (
	ACTION_ENTER  = 1
	ACTION_LEAVE  = 2
	ACTION_ATTACK = 3

	// 挑战次数相关动作
	CHALLENGE_ACTION_QUERY = 1 // 查询挑战次数
	CHALLENGE_ACTION_BUY   = 2 // 购买挑战次数

	// 护盾相关动作
	SHIELD_ACTION_ACTIVATE   = 1 // 激活护盾
	SHIELD_ACTION_DEACTIVATE = 2 // 取消护盾
	SHIELD_ACTION_QUERY      = 3 // 查询护盾状态

	// 护盾相关常量
	SHIELD_DEFAULT_DURATION = 3600 * 8 // 默认护盾持续时间(8小时)
	SHIELD_FREE_COUNT       = 1        // 每日免费护盾次数
	SHIELD_MAX_COUNT        = 5        // 每日最大护盾次数

	MINE_LEVEL_MAX = 7  // 矿点等级上限(1-7级)
	POSITION_MAX   = 16 // 坑位上限
	TEAM_MAX       = 3  // 队伍上限

	// 时间常量
	WEEKLY_START_DAY  = 2  // 每周开始日(周二)
	ACTIVITY_DURATION = 6  // 活动持续天数
	SETTLEMENT_HOUR   = 20 // 结算时间(20:00)
	SETTLEMENT_DAY    = 0  // 结算日(周日)
	REST_START_HOUR   = 23 // 休赛期开始时间(23:00)
	REST_END_HOUR     = 12 // 休赛期结束时间(12:00)

	// 矿脉等级常量
	MINE_LEVEL_PRIVATE_MAX = 3 // 私有矿脉最高等级(1-3级)
	MINE_LEVEL_PUBLIC_MIN  = 4 // 公共矿脉最低等级(4级)
	MINE_LEVEL_PUBLIC_MAX  = 7 // 公共矿脉最高等级(7级)

	// 产出效率
	REST_PERIOD_OUTPUT_RATE = 50 // 休赛期产出效率(50%)

	// 挑战次数相关
	DAILY_CHALLENGE_FREE_COUNT = 5  // 每日免费挑战次数
	DAILY_CHALLENGE_MAX_COUNT  = 10 // 每日最大挑战次数(包括购买)
	CHALLENGE_BUY_MAX_COUNT    = 5  // 每日最大购买次数
)

type MineWarConfig struct {
	Id               int
	KeyId            int
	StartTime        int64 // 开始时间
	PrepareTime      int64 // 准备期
	BattleTime       int64 // 战斗期
	ResultTime       int64 // 结算期
	EndTime          int64 // 总结束时间
	KeepTime         int64 `json:"keep_time"`         // 持续时间
	BoothProtection  int64 `json:"booth_protection"`  // 占位保护时长秒
	AttackNum        int   `json:"attack_num"`        // 每日攻击次数
	ShieldProtection int64 `json:"shield_protection"` // 护盾保护时长秒
	BoothMaxtime     int64 `json:"booth_maxtime"`     // 坑位最长占领时间
	AttackCd         int64 `json:"attack_cd"`         // 队伍攻击CD时间

	db.DataUpdate
}

type MineWarUpdateInfo struct {
	Point  int64   `json:"point"`
	TeamId []int64 `json:"team_id"`
}

func (self *MineWarConfig) Decode() {
}

// ! 将data数据写入数据库
func (self *MineWarConfig) Encode() {
}

// 矿脉等级配置表
type MineWarLevelConfig struct {
	Level          int   `json:"level"`            // 矿脉等级
	UnlockDay      int   `json:"unlock_day"`       // 解锁天数
	MaxAccumTime   int64 `json:"max_accum_time"`   // 最大累积时间(小时)
	BaseOutputRate int64 `json:"base_output_rate"` // 基础产出率(每秒)
	Shield         int64 `json:"shield"`           // 护盾值
	AttackCd       int64 `json:"attack_cd"`        // 攻击冷却时间(秒)
	MaxAttackNum   int   `json:"max_attack_num"`   // 每日最大攻击次数
	IsPublic       int   `json:"is_public"`        // 是否为公共矿脉(0:私有 1:公共)
	MineCount      int   `json:"mine_count"`       // 该等级矿脉数量
}

// 矿脉活动配置表
type MineWarActivityConfig struct {
	ActivityGroup        int `json:"activity_group"`          // 活动组ID
	KeepTime             int `json:"keep_time"`               // 活动持续天数
	StartDay             int `json:"start_day"`               // 开始日期(周几)
	SettlementHour       int `json:"settlement_hour"`         // 结算时间(小时)
	RestStartHour        int `json:"rest_start_hour"`         // 休赛期开始时间
	RestEndHour          int `json:"rest_end_hour"`           // 休赛期结束时间
	RestOutputRate       int `json:"rest_output_rate"`        // 休赛期产出效率(百分比)
	TeamMaxNum           int `json:"team_max_num"`            // 玩家最大队伍数量
	BonusRate            int `json:"bonus_rate"`              // 5级矿脉加成比例(百分比)
	ChallengeFreeCount   int `json:"challenge_free_count"`    // 每日免费挑战次数
	ChallengeMaxCount    int `json:"challenge_max_count"`     // 每日最大挑战次数
	ChallengeBuyMaxCount int `json:"challenge_buy_max_count"` // 每日最大购买次数
	ChallengeBuyCost     int `json:"challenge_buy_cost"`      // 购买挑战次数消耗货币数量
	ShieldFreeCount      int `json:"shield_free_count"`       // 每日免费护盾次数
	ShieldMaxCount       int `json:"shield_max_count"`        // 每日最大护盾次数
	ShieldBuyMaxCount    int `json:"shield_buy_max_count"`    // 每日最大购买护盾次数
	ShieldBuyCost        int `json:"shield_buy_cost"`         // 购买护盾消耗货币数量
	ShieldDuration       int `json:"shield_duration"`         // 护盾持续时间(小时)
}

// 矿脉产出配置表
type MineWarOutputConfig struct {
	Level       int `json:"level"`        // 矿脉等级
	OutputItem1 int `json:"output_item1"` // 产出物品1ID(商店货币)
	OutputNum1  int `json:"output_num1"`  // 产出物品1数量
	OutputItem2 int `json:"output_item2"` // 产出物品2ID(治疗妖姬)
	OutputNum2  int `json:"output_num2"`  // 产出物品2数量
	OutputItem3 int `json:"output_item3"` // 产出物品3ID(积分)
	OutputNum3  int `json:"output_num3"`  // 产出物品3数量
}

type JS_MineInfo struct {
	MineId     int64 `json:"mine_id"`
	TeamType   int   `json:"team_type"`
	StartTime  int64 `json:"start_time"`  // 占领起始时间
	ShieldTime int64 `json:"shield_time"` // 护盾保护起始时间
}

type MineWarMine struct {
	MineId     int64
	GroupId    int // 预留字段 暂不启用 如果以后要做服务器分组的话不用额外加字段了
	SeasonTime int64
	FightInfo  string
	StartTime  int64 // 占领起始时间
	AttackTime int64 // 攻击者的攻击时间
	AttackUid  int64 // 攻击者的uid
	ShieldTime int64 // 护盾保护起始时间

	// 新增字段
	Level        int   `json:"level"`          // 矿脉等级
	ServerId     int   `json:"server_id"`      // 所属服务器ID(1-3级矿脉专属)
	Region       int   `json:"region"`         // 区域ID(1-4，对应四个角落)
	IsPublic     bool  `json:"is_public"`      // 是否为公共矿脉(4-7级)
	UnlockDay    int   `json:"unlock_day"`     // 解锁天数
	IsUnlocked   bool  `json:"is_unlocked"`    // 是否已解锁
	BonusActive  bool  `json:"bonus_active"`   // 是否有产出加成(5级矿脉随机)
	MaxAccumTime int64 `json:"max_accum_time"` // 最大累积时间(秒)

	// 护盾相关字段
	ShieldActive    bool  `json:"shield_active"`     // 护盾是否激活
	ShieldStartTime int64 `json:"shield_start_time"` // 护盾开始时间
	ShieldEndTime   int64 `json:"shield_end_time"`   // 护盾结束时间
	ShieldDuration  int64 `json:"shield_duration"`   // 护盾持续时间(秒)

	fightInfo *model.JS_FightInfo
	db.DataUpdate
}

// ! 将数据库数据写入data-无特殊结构
func (self *MineWarMine) Decode() {
	json.Unmarshal([]byte(self.FightInfo), &self.fightInfo)
}

// ! 将data数据写入数据库
func (self *MineWarMine) Encode() {
	self.FightInfo = utils.HF_JtoA(&self.fightInfo)
}

type MineWarServer struct {
	ServerId   int    `json:"server_id"`
	ServerName string `json:"server_name"`
}

type MineWarPlayer struct {
	Uid               int64           `json:"uid"`
	ServerId          int             `json:"server_id"`
	TeamMine          map[int64]int64 `json:"team_mine"`           // map[队伍id][矿点id]
	ChallengeCount    int             `json:"challenge_count"`     // 今日已使用挑战次数
	ChallengeBuyCount int             `json:"challenge_buy_count"` // 今日已购买挑战次数
	ShieldCount       int             `json:"shield_count"`        // 今日已使用护盾次数
	ShieldBuyCount    int             `json:"shield_buy_count"`    // 今日已购买护盾次数
	LastResetTime     int64           `json:"last_reset_time"`     // 上次重置时间(用于判断是否需要重置)
}

func (self *MineWarInfo) JS_MineInfoChange(mine *MineWarMine) *JS_MineInfo {
	mineInfo := new(JS_MineInfo)
	mineInfo.MineId = mine.MineId
	mineInfo.TeamType = int(mine.fightInfo.Param)
	mineInfo.StartTime = mine.StartTime
	mineInfo.ShieldTime = mine.ShieldTime
	return mineInfo
}

type RPC_MinePointReq struct {
	MineId      int64 `json:"mine_id"`
	Uid         int64 `json:"uid"`
	ServerId    int64 `json:"server_id"`
	TeamId      int64 `json:"team_id"`
	Position    int   `json:"position"`
	AttackPower int64 `json:"attack_power"`
	GroupId     int   `json:"group_id"` // 支线ID
}

type RPC_MinePointRes struct {
	RetCode     int              `json:"ret_code"`
	Points      []*MinePoint     `json:"points"`
	Positions   []*MinePosition  `json:"positions"`
	Teams       []*MineTeam      `json:"teams"`
	OutputNum   int              `json:"output_num"`
	OutputItem  int              `json:"output_item"`
	OutputItems []MineOutputItem `json:"output_items"` // 多种产出物品
}

// 挑战次数相关RPC
type RPC_MineChallengeReq struct {
	Uid     int64 `json:"uid"`
	GroupId int   `json:"group_id"`
	Action  int   `json:"action"` // 1:查询次数 2:购买次数
}

type RPC_MineChallengeRes struct {
	RetCode           int `json:"ret_code"`
	RemainingCount    int `json:"remaining_count"`     // 剩余挑战次数
	UsedCount         int `json:"used_count"`          // 已使用挑战次数
	RemainingBuyCount int `json:"remaining_buy_count"` // 剩余购买次数
	UsedBuyCount      int `json:"used_buy_count"`      // 已购买次数
	BuyCost           int `json:"buy_cost"`            // 购买消耗
}

// 护盾相关RPC
type RPC_MineShieldReq struct {
	Uid     int64 `json:"uid"`
	GroupId int   `json:"group_id"`
	MineId  int64 `json:"mine_id"`
	Action  int   `json:"action"` // 1:激活护盾 2:取消护盾 3:查询状态 4:购买护盾次数
}

type RPC_MineShieldRes struct {
	RetCode           int   `json:"ret_code"`
	ShieldActive      bool  `json:"shield_active"`       // 护盾是否激活
	ShieldStartTime   int64 `json:"shield_start_time"`   // 护盾开始时间
	ShieldEndTime     int64 `json:"shield_end_time"`     // 护盾结束时间
	ShieldDuration    int64 `json:"shield_duration"`     // 护盾持续时间(秒)
	RemainingTime     int64 `json:"remaining_time"`      // 护盾剩余时间(秒)
	RemainingCount    int   `json:"remaining_count"`     // 剩余护盾次数
	UsedCount         int   `json:"used_count"`          // 已使用护盾次数
	RemainingBuyCount int   `json:"remaining_buy_count"` // 剩余购买次数
	UsedBuyCount      int   `json:"used_buy_count"`      // 已购买次数
	BuyCost           int   `json:"buy_cost"`            // 购买消耗
}

type MinePoint struct {
	MineId       int64 `json:"mine_id"`
	OwnerId      int64 `json:"owner_id"`
	Shield       int64 `json:"shield"`
	AttackNum    int   `json:"attack_num"`
	OutputItem   int   `json:"output_item"`
	OutputRate   int64 `json:"output_rate"`
	Level        int   `json:"level"`
	LineIndex    int   `json:"line_index"`
	GroupId      int   `json:"group_id"`       // 支线ID，0表示公共矿点
	ServerId     int   `json:"server_id"`      // 所属服务器ID(1-3级矿脉专属)
	Region       int   `json:"region"`         // 区域ID(1-4，对应四个角落)
	IsPublic     bool  `json:"is_public"`      // 是否为公共矿脉(4-7级)
	UnlockDay    int   `json:"unlock_day"`     // 解锁天数
	IsUnlocked   bool  `json:"is_unlocked"`    // 是否已解锁
	BonusActive  bool  `json:"bonus_active"`   // 是否有产出加成(5级矿脉随机)
	MaxAccumTime int64 `json:"max_accum_time"` // 最大累积时间(秒)
	LastSaveTime int64 `json:"last_save_time"`
	OutputNum    int64 `json:"output_num"`
}

type MinePosition struct {
	PositionId int64 `json:"position_id"`
	MineId     int64 `json:"mine_id"`
	PlayerId   int64 `json:"player_id"`
	EnterTime  int64 `json:"enter_time"`
	LeaveTime  int64 `json:"leave_time"`
	Status     int   `json:"status"`
	GroupId    int   `json:"group_id"`
}

type MineTeam struct {
	TeamId         int64 `json:"team_id"`
	PlayerId       int64 `json:"player_id"`
	MineId         int64 `json:"mine_id"`
	PositionId     int64 `json:"position_id"`
	GroupId        int   `json:"group_id"`
	Status         int   `json:"status"`
	LastAttackTime int64 `json:"last_attack_time"`
}

type MineWarInfo struct {
	MineWarMines       map[int]map[int64]*MineWarMine   // 矿点数据 map[group][mineid]
	MineWarPlayer      map[int]map[int64]*MineWarPlayer // 玩家数据 map[group][uid]
	MineWarServer      map[int]map[int]*MineWarServer   // 参与的服务器数据 map[group][serverid]
	MineWarServerGroup map[int]int                      // 服务器所在的组 map[serverid]group

	MineWarConfig *MineWarConfig
	lock          *sync.RWMutex
}

// ! 矿战管理类
type MineWarMgr struct {
	MineWarInfo *MineWarInfo //!

	MineWarConfigMap         map[int64]*MineWarConfig       //Mine_War_Config
	MineWarLevelConfigMap    map[int]*MineWarLevelConfig    //Mine_War_Level_Config
	MineWarActivityConfigMap map[int]*MineWarActivityConfig //Mine_War_Activity_Config
	MineWarOutputConfigMap   map[int]*MineWarOutputConfig   //Mine_War_Output_Config
}

func (self *MineWarMgr) Save() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.Save()
	}
}

func (self *MineWarInfo) Save() {
	self.lock.RLock()
	defer self.lock.RUnlock()

	// 保存矿脉数据
	for _, group := range self.MineWarMines {
		for _, v := range group {
			v.Encode()
			v.UpdateEx("groupid", v.GroupId)
		}
	}

	// 保存玩家数据(包括挑战次数)
	for _, group := range self.MineWarPlayer {
		for _, player := range group {
			// TODO: 保存玩家挑战次数数据到数据库
			// 这里需要根据实际的数据库表结构来实现
			_ = player // 避免未使用变量警告
		}
	}

	self.MineWarConfig.Update(true, false)
}

func (self *MineWarInfo) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 检查是否在活动期间
	if !GetMineWarMgr().IsActivityPeriod() {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查挑战次数是否足够
	if !self.CheckPlayerChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查玩家队伍数量是否已满
	if !self.CanPlayerAddTeam(req.Uid) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否可以攻击该矿脉
	if !self.CanAttackMine(int(req.ServerId), mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 消耗挑战次数
	if !self.ConsumeChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// TODO: 实现具体的进入矿点逻辑
	res.RetCode = RETCODE_OK
}

// 检查玩家是否可以添加新队伍
func (self *MineWarInfo) CanPlayerAddTeam(uid int64) bool {
	// 从配置表获取最大队伍数量
	maxTeams := TEAM_MAX // 默认值
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxTeams = activityConfig.TeamMaxNum
		}
	}

	// 统计玩家当前的队伍数量
	teamCount := 0
	for _, group := range self.MineWarPlayer {
		if player, ok := group[uid]; ok {
			teamCount += len(player.TeamMine)
		}
	}

	return teamCount < maxTeams
}

// 获取矿脉信息
func (self *MineWarInfo) GetMineWarMine(groupId int, mineId int64) (*MineWarMine, bool) {
	if group, ok := self.MineWarMines[groupId]; ok {
		if mine, ok := group[mineId]; ok {
			return mine, true
		}
	}
	return nil, false
}

// 检查队伍是否可以更换阵容
func (self *MineWarInfo) CanChangeTeamFormation(uid int64, teamId int64) bool {
	// 检查队伍是否正在占据矿脉
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.fightInfo != nil && mine.fightInfo.Uid == uid && mine.fightInfo.Param == teamId {
				return false // 正在占据矿脉，无法更换阵容
			}
		}
	}
	return true
}

func (self *MineWarInfo) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 检查是否在战斗时间
	if !GetMineWarMgr().IsWarTime() {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查挑战次数是否足够
	if !self.CheckPlayerChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否可以攻击该矿脉
	if !self.CanAttackMine(int(req.ServerId), mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查矿脉是否有护盾保护
	if self.IsShieldActive(mine) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 消耗挑战次数
	if !self.ConsumeChallengeCount(req.Uid, req.GroupId) {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// TODO: 实现具体的攻击逻辑
	// 这里应该包括战斗计算、矿脉占领状态更新等

	// 如果攻击成功并占领矿脉，需要取消原有的护盾
	// 这部分逻辑应该在战斗结算后调用 self.OnMineOccupied(mine, req.Uid)

	res.RetCode = RETCODE_OK
}

// 矿脉被占领时的处理
func (self *MineWarInfo) OnMineOccupied(mine *MineWarMine, newOwnerId int64) {
	// 取消护盾
	if mine.ShieldActive {
		mine.ShieldActive = false
		mine.ShieldStartTime = 0
		mine.ShieldEndTime = 0
		mine.ShieldDuration = 0
	}

	// 更新占领者信息
	if mine.fightInfo != nil {
		mine.fightInfo.Uid = newOwnerId
	}

	// 重置开始时间用于产出计算
	mine.StartTime = time.Now().Unix()
}
}

func (self *MineWarInfo) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// TODO: 实现离开矿点逻辑
	res.RetCode = RETCODE_OK
}

func (self *MineWarInfo) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(req.GroupId, req.MineId)
	if !ok {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 检查是否为占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != req.Uid {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}

	// 计算产出
	currentTime := time.Now().Unix()
	outputTime := currentTime - mine.StartTime

	// 限制最大累积时间
	if outputTime > mine.MaxAccumTime {
		outputTime = mine.MaxAccumTime
	}

	// 计算基础产出
	baseRate := self.GetMineBaseOutputRate(mine.Level)
	actualRate := self.GetOutputRate(baseRate)

	// 如果有加成，增加产出
	if mine.BonusActive {
		bonusRate := 150 // 默认50%加成
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				bonusRate = 100 + activityConfig.BonusRate
			}
		}
		actualRate = actualRate * int64(bonusRate) / 100
	}

	// 护盾期间稳定收益：护盾激活期间产出效率提升
	if self.IsShieldActive(mine) {
		// 护盾期间产出效率提升20%
		actualRate = actualRate * 120 / 100
	}

	totalOutput := outputTime * actualRate

	// 更新时间
	mine.StartTime = currentTime

	// TODO: 添加到玩家背包和积分

	res.RetCode = RETCODE_OK
	res.OutputNum = int(totalOutput)
	res.OutputItems = self.GetMineOutputItems(mine.Level)
}

// 获取矿脉基础产出率
func (self *MineWarInfo) GetMineBaseOutputRate(level int) int64 {
	config := GetMineWarMgr().GetMineWarLevelConfig(level)
	if config != nil {
		return config.BaseOutputRate
	}

	// 如果没有配置，使用默认值
	switch level {
	case 1:
		return 10 // 每秒10个货币
	case 2:
		return 15
	case 3:
		return 20
	case 4:
		return 30
	case 5:
		return 45
	case 6:
		return 60
	case 7:
		return 100
	default:
		return 10
	}
}

// 获取矿脉产出配置
func (self *MineWarInfo) GetMineOutputItems(level int) []MineOutputItem {
	config := GetMineWarMgr().GetMineWarOutputConfig(level)
	if config != nil {
		items := make([]MineOutputItem, 0)
		if config.OutputItem1 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem1, ItemNum: config.OutputNum1})
		}
		if config.OutputItem2 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem2, ItemNum: config.OutputNum2})
		}
		if config.OutputItem3 > 0 {
			items = append(items, MineOutputItem{ItemId: config.OutputItem3, ItemNum: config.OutputNum3})
		}
		return items
	}

	// 如果没有配置，返回默认产出
	return []MineOutputItem{
		{ItemId: 10001 + level, ItemNum: 1}, // 示例物品ID
	}
}

// 矿脉产出物品结构
type MineOutputItem struct {
	ItemId  int `json:"item_id"`
	ItemNum int `json:"item_num"`
}

// 更新玩家积分
func (self *MineWarInfo) UpdatePlayerScore(uid int64, serverId int, score int64) {
	// TODO: 实现积分更新逻辑
	// 需要调用排行榜系统更新个人积分、公会积分、服务器积分
}

// 检查玩家挑战次数是否足够
func (self *MineWarInfo) CheckPlayerChallengeCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数(每日0点重置)
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	maxCount := DAILY_CHALLENGE_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ChallengeMaxCount
		}
	}

	return player.ChallengeCount < maxCount
}

// 消耗玩家挑战次数
func (self *MineWarInfo) ConsumeChallengeCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	maxCount := DAILY_CHALLENGE_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ChallengeMaxCount
		}
	}

	if player.ChallengeCount >= maxCount {
		return false
	}

	player.ChallengeCount++
	return true
}

// 购买挑战次数
func (self *MineWarInfo) BuyChallengeCount(uid int64, groupId int) (bool, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	buyMaxCount := CHALLENGE_BUY_MAX_COUNT
	buyCost := 100 // 默认消耗100货币
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			buyMaxCount = activityConfig.ChallengeBuyMaxCount
			buyCost = activityConfig.ChallengeBuyCost
		}
	}

	// 检查是否已达到购买上限
	if player.ChallengeBuyCount >= buyMaxCount {
		return false, 0
	}

	player.ChallengeBuyCount++
	return true, buyCost
}

// 检查并重置每日挑战次数
func (self *MineWarInfo) CheckAndResetDailyChallengeCount(player *MineWarPlayer) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayUnix := today.Unix()

	// 如果上次重置时间不是今天，则重置挑战次数和护盾次数
	if player.LastResetTime < todayUnix {
		player.ChallengeCount = 0
		player.ChallengeBuyCount = 0
		player.ShieldCount = 0
		player.ShieldBuyCount = 0
		player.LastResetTime = todayUnix
	}
}

// 获取或创建矿战玩家数据
func (self *MineWarInfo) GetOrCreateMineWarPlayer(uid int64, groupId int) *MineWarPlayer {
	// 确保组存在
	if _, ok := self.MineWarPlayer[groupId]; !ok {
		self.MineWarPlayer[groupId] = make(map[int64]*MineWarPlayer)
	}

	// 获取或创建玩家数据
	if player, ok := self.MineWarPlayer[groupId][uid]; ok {
		return player
	}

	// 创建新的玩家数据
	player := &MineWarPlayer{
		Uid:               uid,
		ServerId:          0, // 需要从其他地方获取
		TeamMine:          make(map[int64]int64),
		ChallengeCount:    0,
		ChallengeBuyCount: 0,
		ShieldCount:       0,
		ShieldBuyCount:    0,
		LastResetTime:     0,
	}

	self.MineWarPlayer[groupId][uid] = player
	return player
}

// 获取玩家剩余挑战次数信息
func (self *MineWarInfo) GetPlayerChallengeInfo(uid int64, groupId int) (int, int, int, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置挑战次数
	self.CheckAndResetDailyChallengeCount(player)

	// 获取配置
	maxCount := DAILY_CHALLENGE_MAX_COUNT
	buyMaxCount := CHALLENGE_BUY_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ChallengeMaxCount
			buyMaxCount = activityConfig.ChallengeBuyMaxCount
		}
	}

	remainingCount := maxCount - player.ChallengeCount
	if remainingCount < 0 {
		remainingCount = 0
	}

	remainingBuyCount := buyMaxCount - player.ChallengeBuyCount
	if remainingBuyCount < 0 {
		remainingBuyCount = 0
	}

	return remainingCount, player.ChallengeCount, remainingBuyCount, player.ChallengeBuyCount
}

// 检查矿脉是否有护盾保护
func (self *MineWarInfo) IsShieldActive(mine *MineWarMine) bool {
	if !mine.ShieldActive {
		return false
	}

	currentTime := time.Now().Unix()
	if currentTime > mine.ShieldEndTime {
		// 护盾已过期，自动取消
		mine.ShieldActive = false
		mine.ShieldStartTime = 0
		mine.ShieldEndTime = 0
		return false
	}

	return true
}

// 激活矿脉护盾
func (self *MineWarInfo) ActivateShield(uid int64, groupId int, mineId int64) bool {
	// 检查玩家护盾次数
	if !self.CheckPlayerShieldCount(uid, groupId) {
		return false
	}

	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(groupId, mineId)
	if !ok {
		return false
	}

	// 检查是否为矿脉占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != uid {
		return false
	}

	// 检查护盾是否已经激活
	if self.IsShieldActive(mine) {
		return false
	}

	// 获取护盾持续时间
	duration := int64(SHIELD_DEFAULT_DURATION)
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			duration = int64(activityConfig.ShieldDuration) * utils.HOUR_SECS
		}
	}

	// 激活护盾
	currentTime := time.Now().Unix()
	mine.ShieldActive = true
	mine.ShieldStartTime = currentTime
	mine.ShieldEndTime = currentTime + duration
	mine.ShieldDuration = duration

	// 消耗护盾次数
	self.ConsumeShieldCount(uid, groupId)

	return true
}

// 取消矿脉护盾
func (self *MineWarInfo) DeactivateShield(uid int64, groupId int, mineId int64) bool {
	// 获取矿脉信息
	mine, ok := self.GetMineWarMine(groupId, mineId)
	if !ok {
		return false
	}

	// 检查是否为矿脉占领者
	if mine.fightInfo == nil || mine.fightInfo.Uid != uid {
		return false
	}

	// 检查护盾是否激活
	if !mine.ShieldActive {
		return false
	}

	// 取消护盾
	mine.ShieldActive = false
	mine.ShieldStartTime = 0
	mine.ShieldEndTime = 0
	mine.ShieldDuration = 0

	return true
}

// 检查玩家护盾次数是否足够
func (self *MineWarInfo) CheckPlayerShieldCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	maxCount := SHIELD_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ShieldMaxCount
		}
	}

	return player.ShieldCount < maxCount
}

// 消耗玩家护盾次数
func (self *MineWarInfo) ConsumeShieldCount(uid int64, groupId int) bool {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	maxCount := SHIELD_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ShieldMaxCount
		}
	}

	if player.ShieldCount >= maxCount {
		return false
	}

	player.ShieldCount++
	return true
}

// 购买护盾次数
func (self *MineWarInfo) BuyShieldCount(uid int64, groupId int) (bool, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	buyMaxCount := SHIELD_MAX_COUNT
	buyCost := 200 // 默认消耗200货币
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			buyMaxCount = activityConfig.ShieldBuyMaxCount
			buyCost = activityConfig.ShieldBuyCost
		}
	}

	// 检查是否已达到购买上限
	if player.ShieldBuyCount >= buyMaxCount {
		return false, 0
	}

	player.ShieldBuyCount++
	return true, buyCost
}

// 检查并重置每日护盾次数
func (self *MineWarInfo) CheckAndResetDailyShieldCount(player *MineWarPlayer) {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayUnix := today.Unix()

	// 如果上次重置时间不是今天，则重置护盾次数
	if player.LastResetTime < todayUnix {
		player.ShieldCount = 0
		player.ShieldBuyCount = 0
		// 注意：这里不重置LastResetTime，因为挑战次数重置时会设置
	}
}

// 获取玩家护盾次数信息
func (self *MineWarInfo) GetPlayerShieldInfo(uid int64, groupId int) (int, int, int, int) {
	player := self.GetOrCreateMineWarPlayer(uid, groupId)

	// 检查是否需要重置护盾次数
	self.CheckAndResetDailyShieldCount(player)

	// 获取配置
	maxCount := SHIELD_MAX_COUNT
	buyMaxCount := SHIELD_MAX_COUNT
	if self.MineWarConfig != nil {
		activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
		if activityConfig != nil {
			maxCount = activityConfig.ShieldMaxCount
			buyMaxCount = activityConfig.ShieldBuyMaxCount
		}
	}

	remainingCount := maxCount - player.ShieldCount
	if remainingCount < 0 {
		remainingCount = 0
	}

	remainingBuyCount := buyMaxCount - player.ShieldBuyCount
	if remainingBuyCount < 0 {
		remainingBuyCount = 0
	}

	return remainingCount, player.ShieldCount, remainingBuyCount, player.ShieldBuyCount
}

// 获取矿脉护盾状态
func (self *MineWarInfo) GetMineShieldStatus(mineId int64, groupId int) (bool, int64, int64, int64) {
	mine, ok := self.GetMineWarMine(groupId, mineId)
	if !ok {
		return false, 0, 0, 0
	}

	// 检查护盾是否激活
	isActive := self.IsShieldActive(mine)

	var remainingTime int64 = 0
	if isActive {
		currentTime := time.Now().Unix()
		remainingTime = mine.ShieldEndTime - currentTime
		if remainingTime < 0 {
			remainingTime = 0
		}
	}

	return isActive, mine.ShieldStartTime, mine.ShieldEndTime, remainingTime
}

// 处理挑战次数相关请求
func (self *MineWarInfo) HandleMineChallenge(req *RPC_MineChallengeReq, res *RPC_MineChallengeRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	switch req.Action {
	case 1: // 查询挑战次数
		remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerChallengeInfo(req.Uid, req.GroupId)

		// 获取购买消耗
		buyCost := 100 // 默认消耗
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				buyCost = activityConfig.ChallengeBuyCost
			}
		}

		res.RetCode = RETCODE_OK
		res.RemainingCount = remainingCount
		res.UsedCount = usedCount
		res.RemainingBuyCount = remainingBuyCount
		res.UsedBuyCount = usedBuyCount
		res.BuyCost = buyCost

	case 2: // 购买挑战次数
		success, cost := self.BuyChallengeCount(req.Uid, req.GroupId)
		if success {
			res.RetCode = RETCODE_OK
			res.BuyCost = cost

			// 返回更新后的挑战次数信息
			remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerChallengeInfo(req.Uid, req.GroupId)
			res.RemainingCount = remainingCount
			res.UsedCount = usedCount
			res.RemainingBuyCount = remainingBuyCount
			res.UsedBuyCount = usedBuyCount
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	default:
		res.RetCode = RETCODE_DATA_ERROR
	}
}

// 处理护盾相关请求
func (self *MineWarInfo) HandleMineShield(req *RPC_MineShieldReq, res *RPC_MineShieldRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	switch req.Action {
	case SHIELD_ACTION_ACTIVATE: // 激活护盾
		success := self.ActivateShield(req.Uid, req.GroupId, req.MineId)
		if success {
			res.RetCode = RETCODE_OK
			// 返回护盾状态
			isActive, startTime, endTime, remainingTime := self.GetMineShieldStatus(req.MineId, req.GroupId)
			res.ShieldActive = isActive
			res.ShieldStartTime = startTime
			res.ShieldEndTime = endTime
			res.RemainingTime = remainingTime

			// 返回玩家护盾次数信息
			remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerShieldInfo(req.Uid, req.GroupId)
			res.RemainingCount = remainingCount
			res.UsedCount = usedCount
			res.RemainingBuyCount = remainingBuyCount
			res.UsedBuyCount = usedBuyCount
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	case SHIELD_ACTION_DEACTIVATE: // 取消护盾
		success := self.DeactivateShield(req.Uid, req.GroupId, req.MineId)
		if success {
			res.RetCode = RETCODE_OK
			res.ShieldActive = false
			res.ShieldStartTime = 0
			res.ShieldEndTime = 0
			res.RemainingTime = 0
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	case SHIELD_ACTION_QUERY: // 查询护盾状态
		// 获取矿脉护盾状态
		isActive, startTime, endTime, remainingTime := self.GetMineShieldStatus(req.MineId, req.GroupId)
		res.ShieldActive = isActive
		res.ShieldStartTime = startTime
		res.ShieldEndTime = endTime
		res.RemainingTime = remainingTime

		// 获取玩家护盾次数信息
		remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerShieldInfo(req.Uid, req.GroupId)
		res.RemainingCount = remainingCount
		res.UsedCount = usedCount
		res.RemainingBuyCount = remainingBuyCount
		res.UsedBuyCount = usedBuyCount

		// 获取购买消耗
		buyCost := 200 // 默认消耗
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				buyCost = activityConfig.ShieldBuyCost
			}
		}
		res.BuyCost = buyCost
		res.RetCode = RETCODE_OK

	case 4: // 购买护盾次数
		success, cost := self.BuyShieldCount(req.Uid, req.GroupId)
		if success {
			res.RetCode = RETCODE_OK
			res.BuyCost = cost

			// 返回更新后的护盾次数信息
			remainingCount, usedCount, remainingBuyCount, usedBuyCount := self.GetPlayerShieldInfo(req.Uid, req.GroupId)
			res.RemainingCount = remainingCount
			res.UsedCount = usedCount
			res.RemainingBuyCount = remainingBuyCount
			res.UsedBuyCount = usedBuyCount
		} else {
			res.RetCode = RETCODE_DATA_ERROR
		}

	default:
		res.RetCode = RETCODE_DATA_ERROR
	}
}

func (self *MineWarInfo) Update() {
	// TODO: 实现更新逻辑
}

func (self *MineWarInfo) OnTimer() {
	// TODO: 实现定时器逻辑
	self.CheckMineUnlock()
	self.UpdateDailyBonus()
	self.CheckWeeklySettlement()
	self.CheckDailyReset()
	self.CheckShieldExpiration()
}

// 检查护盾过期
func (self *MineWarInfo) CheckShieldExpiration() {
	currentTime := time.Now().Unix()

	// 检查所有矿脉的护盾状态
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.ShieldActive && currentTime > mine.ShieldEndTime {
				// 护盾已过期，自动取消
				mine.ShieldActive = false
				mine.ShieldStartTime = 0
				mine.ShieldEndTime = 0
				mine.ShieldDuration = 0
			}
		}
	}
}

// 检查每日重置(0点重置挑战次数)
func (self *MineWarInfo) CheckDailyReset() {
	t := time.Now()

	// 每天0点执行重置
	if t.Hour() != 0 || t.Minute() != 0 {
		return
	}

	// 重置所有玩家的挑战次数
	for _, group := range self.MineWarPlayer {
		for _, player := range group {
			self.CheckAndResetDailyChallengeCount(player)
		}
	}
}

// 检查周结算
func (self *MineWarInfo) CheckWeeklySettlement() {
	t := time.Now()

	// 检查是否为周日20:00
	if t.Weekday() != time.Sunday || t.Hour() != SETTLEMENT_HOUR {
		return
	}

	// 执行结算
	self.DoWeeklySettlement()
}

// 执行周结算
func (self *MineWarInfo) DoWeeklySettlement() {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 计算最终排名
	guildRanking := self.CalculateGuildRanking()
	playerRanking := self.CalculatePlayerRanking()
	serverRanking := self.CalculateServerRanking()

	// 发放奖励
	self.DistributeGuildRewards(guildRanking)
	self.DistributePlayerRewards(playerRanking)
	self.DistributeServerRewards(serverRanking)

	// 清理数据，准备下一轮
	self.PrepareNextSeason()
}

// 计算公会排名
func (self *MineWarInfo) CalculateGuildRanking() []GuildRankInfo {
	// TODO: 实现公会积分排名逻辑
	return make([]GuildRankInfo, 0)
}

// 计算个人排名
func (self *MineWarInfo) CalculatePlayerRanking() []PlayerRankInfo {
	// TODO: 实现个人积分排名逻辑
	return make([]PlayerRankInfo, 0)
}

// 计算服务器排名
func (self *MineWarInfo) CalculateServerRanking() []ServerRankInfo {
	// TODO: 实现服务器积分排名逻辑
	return make([]ServerRankInfo, 0)
}

// 发放公会奖励
func (self *MineWarInfo) DistributeGuildRewards(ranking []GuildRankInfo) {
	// TODO: 实现公会奖励发放逻辑
}

// 发放个人奖励
func (self *MineWarInfo) DistributePlayerRewards(ranking []PlayerRankInfo) {
	// TODO: 实现个人奖励发放逻辑
}

// 发放服务器奖励
func (self *MineWarInfo) DistributeServerRewards(ranking []ServerRankInfo) {
	// TODO: 实现服务器奖励发放逻辑
}

// 准备下一赛季
func (self *MineWarInfo) PrepareNextSeason() {
	// TODO: 清理数据，重置状态，准备下一轮活动
}

// 排名信息结构体
type GuildRankInfo struct {
	GuildId int64 `json:"guild_id"`
	Score   int64 `json:"score"`
	Rank    int   `json:"rank"`
}

type PlayerRankInfo struct {
	Uid   int64 `json:"uid"`
	Score int64 `json:"score"`
	Rank  int   `json:"rank"`
}

type ServerRankInfo struct {
	ServerId int   `json:"server_id"`
	Score    int64 `json:"score"`
	Rank     int   `json:"rank"`
}

// 检查矿脉解锁
func (self *MineWarInfo) CheckMineUnlock() {
	if !GetMineWarMgr().IsActivityPeriod() {
		return
	}

	// 计算活动开始后的天数
	activityDay := self.GetActivityDay()

	// 检查所有矿脉的解锁状态
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if !mine.IsUnlocked && activityDay >= mine.UnlockDay {
				mine.IsUnlocked = true
				// TODO: 发送解锁通知
			}
		}
	}
}

// 更新每日加成(5级矿脉随机获得产出加成)
func (self *MineWarInfo) UpdateDailyBonus() {
	t := time.Now()
	// 每天0点更新加成
	if t.Hour() != 0 || t.Minute() != 0 {
		return
	}

	// 重置所有5级矿脉的加成状态
	level5Mines := make([]*MineWarMine, 0)
	for _, group := range self.MineWarMines {
		for _, mine := range group {
			if mine.Level == 5 && mine.IsUnlocked {
				mine.BonusActive = false
				level5Mines = append(level5Mines, mine)
			}
		}
	}

	// 随机选择一个5级矿脉获得加成
	if len(level5Mines) > 0 {
		randomIndex := utils.HF_RandInt(0, len(level5Mines)-1)
		level5Mines[randomIndex].BonusActive = true
		// TODO: 发送加成通知
	}
}

// 获取活动开始后的天数
func (self *MineWarInfo) GetActivityDay() int {
	if self.MineWarConfig == nil {
		return 0
	}

	now := time.Now().Unix()
	startTime := self.MineWarConfig.StartTime
	daysPassed := (now - startTime) / utils.DAY_SECS

	return int(daysPassed) + 1 // 第一天为第1天
}

// 检查矿脉是否可以攻击
func (self *MineWarInfo) CanAttackMine(attackerServerId int, targetMine *MineWarMine) bool {
	// 检查是否在战斗时间
	if !GetMineWarMgr().IsWarTime() {
		return false
	}

	// 检查矿脉是否已解锁
	if !targetMine.IsUnlocked {
		return false
	}

	// 1-3级矿脉只能本服务器攻击
	if targetMine.Level <= MINE_LEVEL_PRIVATE_MAX {
		return targetMine.ServerId == attackerServerId
	}

	// 4-7级矿脉为公共矿脉，所有服务器都可以攻击
	return true
}

// 计算产出效率(休赛期降低效率)
func (self *MineWarInfo) GetOutputRate(baseRate int64) int64 {
	if GetMineWarMgr().IsRestPeriod() {
		// 从配置表获取休赛期产出效率
		restRate := REST_PERIOD_OUTPUT_RATE // 默认50%
		if self.MineWarConfig != nil {
			activityConfig := GetMineWarMgr().GetMineWarActivityConfig(self.MineWarConfig.KeyId / 1000)
			if activityConfig != nil {
				restRate = activityConfig.RestOutputRate
			}
		}
		return baseRate * int64(restRate) / 100
	}
	return baseRate
}

// ! 矿战管理类
var s_mineWarMgr *MineWarMgr = nil

func GetMineWarMgr() *MineWarMgr {
	if s_mineWarMgr == nil {
		s_mineWarMgr = new(MineWarMgr)
		s_mineWarMgr.LoadCsv()
	}
	return s_mineWarMgr
}

func (self *MineWarMgr) LoadCsv() {
	self.MineWarConfigMap = make(map[int64]*MineWarConfig)

	// 加载矿脉等级配置
	self.MineWarLevelConfigMap = make(map[int]*MineWarLevelConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Level_Config", &self.MineWarLevelConfigMap)

	// 加载矿脉活动配置
	self.MineWarActivityConfigMap = make(map[int]*MineWarActivityConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Activity_Config", &self.MineWarActivityConfigMap)

	// 加载矿脉产出配置
	self.MineWarOutputConfigMap = make(map[int]*MineWarOutputConfig)
	utils.GetCsvUtilMgr().LoadCsv("Mine_War_Output_Config", &self.MineWarOutputConfigMap)

	return
}

// 获取矿脉等级配置
func (self *MineWarMgr) GetMineWarLevelConfig(level int) *MineWarLevelConfig {
	if config, ok := self.MineWarLevelConfigMap[level]; ok {
		return config
	}
	return nil
}

// 获取矿脉活动配置
func (self *MineWarMgr) GetMineWarActivityConfig(group int) *MineWarActivityConfig {
	if config, ok := self.MineWarActivityConfigMap[group]; ok {
		return config
	}
	return nil
}

// 获取矿脉产出配置
func (self *MineWarMgr) GetMineWarOutputConfig(level int) *MineWarOutputConfig {
	if config, ok := self.MineWarOutputConfigMap[level]; ok {
		return config
	}
	return nil
}

func (self *MineWarMgr) CheckMineWar(req *RPC_UpdateActivityReq) {
	if self.MineWarInfo.MineWarConfig.EndTime == 0 {
		self.MineWarInfo.InitMineWarConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.GetAllData()
	} else if self.MineWarInfo.MineWarConfig.EndTime != req.EndTime {
		self.MineWarInfo.InitMineWarConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.MineWarInfo.CleanMineWar()
	}
}

func (self *MineWarInfo) InitMineWarConfig(starttime, rewardtime, enttime int64, keyid int) {
	self.lock.Lock()
	defer self.lock.Unlock()
	self.MineWarConfig.KeyId = keyid
	self.MineWarConfig.StartTime = starttime
	self.MineWarConfig.EndTime = enttime

	// 从配置表获取活动配置
	activityConfig := GetMineWarMgr().GetMineWarActivityConfig(keyid / 1000)
	if activityConfig != nil {
		// 使用配置表中的时间设置
		self.MineWarConfig.PrepareTime = self.MineWarConfig.StartTime + 1*utils.DAY_SECS
		self.MineWarConfig.BattleTime = self.MineWarConfig.PrepareTime + int64(activityConfig.KeepTime)*utils.DAY_SECS
		self.MineWarConfig.ResultTime = self.MineWarConfig.BattleTime + 1*utils.DAY_SECS

		// 设置其他配置
		self.MineWarConfig.KeepTime = int64(activityConfig.KeepTime)
		self.MineWarConfig.AttackNum = activityConfig.TeamMaxNum
	} else {
		// 如果没有配置，使用默认值
		self.MineWarConfig.PrepareTime = self.MineWarConfig.StartTime + 1*utils.DAY_SECS
		self.MineWarConfig.BattleTime = self.MineWarConfig.PrepareTime + 5*utils.DAY_SECS
		self.MineWarConfig.ResultTime = self.MineWarConfig.BattleTime + 1*utils.DAY_SECS
	}
}

func (self *MineWarInfo) CleanMineWar() {
	self.lock.Lock()
	defer self.lock.Unlock()
	for _, group := range self.MineWarMines {
		for _, v := range group {
			if v.SeasonTime != self.MineWarConfig.EndTime {
				v.SeasonTime = self.MineWarConfig.EndTime
				v.fightInfo = NewMineWarFightInfo(v.MineId)
				v.StartTime = 0
				v.ShieldTime = 0
				v.AttackUid = 0
				v.AttackTime = 0

				// 重新初始化矿脉属性
				v.InitMineProperties(v.MineId)
				v.Decode()
			}
		}
	}
	self.MineWarPlayer = make(map[int]map[int64]*MineWarPlayer)
	self.MineWarServer = make(map[int]map[int]*MineWarServer)
	self.MineWarServerGroup = make(map[int]int)
}

// 初始化服务器矿脉分配
func (self *MineWarInfo) InitServerMineAllocation(groupId int, serverIds []int) {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 确保组存在
	if _, ok := self.MineWarMines[groupId]; !ok {
		self.MineWarMines[groupId] = make(map[int64]*MineWarMine)
	}

	// 为每个服务器分配1-3级矿脉到四个角落
	for i, serverId := range serverIds {
		region := (i % 4) + 1 // 分配到4个区域

		// 为每个服务器在指定区域创建1-3级矿脉
		for level := 1; level <= MINE_LEVEL_PRIVATE_MAX; level++ {
			// 生成矿脉ID: 服务器ID * 1000 + 区域 * 100 + 等级
			mineId := int64(serverId*1000 + region*100 + level)

			mine := NewMineWarUser(mineId, groupId)
			mine.ServerId = serverId
			mine.Region = region
			mine.Level = level
			mine.IsPublic = false
			mine.IsUnlocked = true // 1-3级矿脉默认解锁

			self.MineWarMines[groupId][mineId] = mine
		}
	}

	// 创建公共矿脉(4-7级)
	self.InitPublicMines(groupId)
}

// 初始化公共矿脉
func (self *MineWarInfo) InitPublicMines(groupId int) {
	// 创建4-7级公共矿脉，分布在地图中央区域
	for level := MINE_LEVEL_PUBLIC_MIN; level <= MINE_LEVEL_PUBLIC_MAX; level++ {
		// 从配置表获取矿脉数量
		mineCount := level - 2 // 默认值：4级2个，5级3个，6级4个，7级5个
		config := GetMineWarMgr().GetMineWarLevelConfig(level)
		if config != nil && config.MineCount > 0 {
			mineCount = config.MineCount
		}

		for i := 1; i <= mineCount; i++ {
			// 生成公共矿脉ID: 9000000 + 等级 * 1000 + 序号
			mineId := int64(9000000 + level*1000 + i)

			mine := NewMineWarUser(mineId, groupId)
			mine.ServerId = 0 // 公共矿脉不属于特定服务器
			mine.Region = 0   // 公共区域
			mine.Level = level
			mine.IsPublic = true
			mine.IsUnlocked = (level == 4) // 4级矿脉默认解锁，其他需要等待

			self.MineWarMines[groupId][mineId] = mine
		}
	}
}

// 根据配置表初始化所有矿脉
func (self *MineWarInfo) InitMinesFromConfig(groupId int, serverIds []int) {
	// 确保组存在
	if _, ok := self.MineWarMines[groupId]; !ok {
		self.MineWarMines[groupId] = make(map[int64]*MineWarMine)
	}

	// 初始化所有等级的矿脉
	for level := 1; level <= MINE_LEVEL_MAX; level++ {
		config := GetMineWarMgr().GetMineWarLevelConfig(level)
		if config == nil {
			continue
		}

		if config.IsPublic == 0 {
			// 私有矿脉，为每个服务器创建
			for i, serverId := range serverIds {
				region := (i % 4) + 1 // 分配到4个区域

				for j := 1; j <= config.MineCount; j++ {
					// 生成矿脉ID: 服务器ID * 10000 + 等级 * 1000 + 序号
					mineId := int64(serverId*10000 + level*1000 + j)

					mine := NewMineWarUser(mineId, groupId)
					mine.ServerId = serverId
					mine.Region = region
					mine.Level = level
					mine.IsPublic = false
					mine.IsUnlocked = (level <= MINE_LEVEL_PRIVATE_MAX) // 1-3级默认解锁

					self.MineWarMines[groupId][mineId] = mine
				}
			}
		} else {
			// 公共矿脉
			for i := 1; i <= config.MineCount; i++ {
				// 生成公共矿脉ID: 9000000 + 等级 * 1000 + 序号
				mineId := int64(9000000 + level*1000 + i)

				mine := NewMineWarUser(mineId, groupId)
				mine.ServerId = 0 // 公共矿脉不属于特定服务器
				mine.Region = 0   // 公共区域
				mine.Level = level
				mine.IsPublic = true
				mine.IsUnlocked = (level == 4) // 4级矿脉默认解锁，其他需要等待

				self.MineWarMines[groupId][mineId] = mine
			}
		}
	}
}

func NewMineWarFightInfo(id int64) *model.JS_FightInfo {
	data := new(model.JS_FightInfo)
	data.Rankid = int(id)
	return data
}

func NewMineWarUser(mineId int64, groupid int) *MineWarMine {
	data := new(MineWarMine)
	data.MineId = mineId
	data.GroupId = groupid
	data.SeasonTime = GetMineWarMgr().MineWarInfo.MineWarConfig.EndTime
	data.fightInfo = NewMineWarFightInfo(mineId)
	data.StartTime = 0

	// 根据矿点ID初始化矿脉属性
	data.InitMineProperties(mineId)

	db.InsertTable("tbl_minewaruser", data, 0, false)
	data.Init("tbl_minewaruser", data, false)
	return data
}

// 初始化矿脉属性
func (self *MineWarMine) InitMineProperties(mineId int64) {
	// 根据矿点ID确定等级和属性
	// 这里简化处理，实际应该从配置表读取
	level := int((mineId % 7) + 1) // 1-7级
	self.Level = level

	// 从配置表获取矿脉等级配置
	config := GetMineWarMgr().GetMineWarLevelConfig(level)
	if config != nil {
		self.UnlockDay = config.UnlockDay
		self.MaxAccumTime = config.MaxAccumTime * utils.HOUR_SECS // 配置表中是小时，转换为秒
		self.IsPublic = (config.IsPublic == 1)
		self.IsUnlocked = (level <= MINE_LEVEL_PRIVATE_MAX) // 1-3级默认解锁
	} else {
		// 如果没有配置，使用默认值
		self.IsPublic = level >= MINE_LEVEL_PUBLIC_MIN

		// 设置解锁天数
		switch level {
		case 1, 2, 3:
			self.UnlockDay = 1 // 1-3级矿脉第一天就解锁
			self.IsUnlocked = true
		case 4:
			self.UnlockDay = 2 // 4级矿脉第2天解锁
		case 5:
			self.UnlockDay = 3 // 5级矿脉第3天解锁
		case 6:
			self.UnlockDay = 4 // 6级矿脉第4天解锁
		case 7:
			self.UnlockDay = 5 // 7级矿脉第5天解锁
		}

		// 设置最大累积时间(根据等级不同)
		switch level {
		case 1, 2:
			self.MaxAccumTime = 4 * utils.HOUR_SECS // 4小时
		case 3, 4:
			self.MaxAccumTime = 6 * utils.HOUR_SECS // 6小时
		case 5, 6:
			self.MaxAccumTime = 8 * utils.HOUR_SECS // 8小时
		case 7:
			self.MaxAccumTime = 12 * utils.HOUR_SECS // 12小时
		}
	}

	// 设置区域(1-4，对应四个角落)
	self.Region = int((mineId % 4) + 1)

	// 初始化其他属性
	self.BonusActive = false

	// 初始化护盾相关属性
	self.ShieldActive = false
	self.ShieldStartTime = 0
	self.ShieldEndTime = 0
	self.ShieldDuration = 0
}

func (self *MineWarMgr) NewMineWarInfo() *MineWarInfo {
	data := new(MineWarInfo)
	data.MineWarMines = make(map[int]map[int64]*MineWarMine, 0)
	data.MineWarPlayer = make(map[int]map[int64]*MineWarPlayer, 0)
	data.MineWarServer = make(map[int]map[int]*MineWarServer, 0)
	data.MineWarServerGroup = make(map[int]int)
	data.lock = new(sync.RWMutex)
	return data
}

// ! 从数据库载入数据
func (self *MineWarMgr) GetAllData() {
	//计算赛季刷新时间
	if self.MineWarInfo == nil {
		self.MineWarInfo = self.NewMineWarInfo()
	}

	if self.MineWarInfo.MineWarConfig == nil {
		self.MineWarInfo.MineWarConfig = new(MineWarConfig)
		queryConfigStr := fmt.Sprintf("select * from `%s` limit 1;", "tbl_minewarconfig")
		ret := db.GetDBMgr().DBUser.GetOneData(queryConfigStr, self.MineWarInfo.MineWarConfig, "tbl_minewarconfig", 0)
		if ret == true {
			self.MineWarInfo.MineWarConfig.Decode()
		} else {
			self.MineWarInfo.MineWarConfig.Id = 1
			db.InsertTable("tbl_minewarconfig", self.MineWarInfo.MineWarConfig, 0, false)
		}
		self.MineWarInfo.MineWarConfig.Init("tbl_minewarconfig", self.MineWarInfo.MineWarConfig, false)
	}

	if self.MineWarInfo.MineWarConfig.EndTime == 0 {
		return
	}

	queryStr := fmt.Sprintf("select * from `tbl_minewaruser`;")
	var msg MineWarMine
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*MineWarMine)
		_, ok := self.MineWarInfo.MineWarMines[data.GroupId]
		if !ok {
			self.MineWarInfo.MineWarMines[data.GroupId] = make(map[int64]*MineWarMine)
		}

		self.MineWarInfo.MineWarMines[data.GroupId][data.MineId] = data
		data.Init("tbl_minewaruser", data, false)

		if data.SeasonTime != self.MineWarInfo.MineWarConfig.EndTime {
			data.FightInfo = ""
			data.SeasonTime = self.MineWarInfo.MineWarConfig.EndTime
		}
		data.Decode()

		if data != nil && data.fightInfo != nil {
			// TODO: 添加服务器列表和玩家管理逻辑
		}
	}
	return
}

func (self *MineWarMgr) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.EnterMinePoint(req, res)
}

func (self *MineWarMgr) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.AttackMinePoint(req, res)
}

func (self *MineWarMgr) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.LeaveMinePoint(req, res)
}

func (self *MineWarMgr) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.GetMineOutput(req, res)
}

// 处理挑战次数相关RPC
func (self *MineWarMgr) HandleMineChallenge(req *RPC_MineChallengeReq, res *RPC_MineChallengeRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.HandleMineChallenge(req, res)
}

// 处理护盾相关RPC
func (self *MineWarMgr) HandleMineShield(req *RPC_MineShieldReq, res *RPC_MineShieldRes) {
	if self.MineWarInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.MineWarInfo.HandleMineShield(req, res)
}

func (self *MineWarMgr) Update() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.Update()
	}
}

// ! 主逻辑循环
func (self *MineWarMgr) Run() {
	//! 每秒执行一次
	ticker := time.NewTicker(time.Minute * 1)
	for {
		<-ticker.C
		self.OnTimer()
	}
	ticker.Stop()
}

// ! 主逻辑循环
func (self *MineWarMgr) OnTimer() {
	if self.MineWarInfo != nil {
		self.MineWarInfo.lock.Lock()
		defer self.MineWarInfo.lock.Unlock()
		self.MineWarInfo.OnTimer()
	}
}

func (self *MineWarMgr) IsWarTime() bool {
	t := time.Now()

	// 检查是否在活动期间(周二到周日)
	if t.Weekday() < time.Tuesday || t.Weekday() > time.Sunday {
		return false // 不在活动期间
	}

	// 如果是周日，检查是否已过结算时间
	if t.Weekday() == time.Sunday && t.Hour() >= SETTLEMENT_HOUR {
		return false // 活动已结束
	}

	// 检查是否在休赛期(每天23:00到次日12:00)
	if t.Hour() >= REST_START_HOUR || t.Hour() < REST_END_HOUR {
		return false // 在休赛期，无法战斗
	}

	return true // 可以战斗
}

// 检查是否在休赛期
func (self *MineWarMgr) IsRestPeriod() bool {
	t := time.Now()
	return t.Hour() >= REST_START_HOUR || t.Hour() < REST_END_HOUR
}

// 检查是否在活动期间
func (self *MineWarMgr) IsActivityPeriod() bool {
	t := time.Now()

	// 检查是否在活动期间(周二到周日)
	if t.Weekday() < time.Tuesday || t.Weekday() > time.Sunday {
		return false
	}

	// 如果是周日，检查是否已过结算时间
	if t.Weekday() == time.Sunday && t.Hour() >= SETTLEMENT_HOUR {
		return false
	}

	return true
}
